2025-06-21 13:04:55.039 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-21 13:04:55.106 [main] INFO  c.u.k.KingdeeDataApplication - Starting KingdeeDataApplication using Java 17.0.11 with PID 66452 (/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes started by stong_mac in /Volumes/diskD/work/jiulian/javacode/KingdeeData)
2025-06-21 13:04:55.107 [main] INFO  c.u.k.KingdeeDataApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-21 13:04:56.688 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-21 13:04:56.706 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-21 13:04:56.708 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 13:04:56.708 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-21 13:04:56.783 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 13:04:56.783 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1623 ms
2025-06-21 13:04:57.121 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Starting...
2025-06-21 13:04:57.946 [main] INFO  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Added connection ConnectionID:1 ClientConnectionId: 02034003-fd1a-422f-bf3e-3b3161801075
2025-06-21 13:04:57.950 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Start completed.
2025-06-21 13:04:57.953 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Starting...
2025-06-21 13:04:58.201 [main] INFO  com.zaxxer.hikari.pool.HikariPool - tempdb - Added connection com.mysql.cj.jdbc.ConnectionImpl@1eb906f3
2025-06-21 13:04:58.202 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Start completed.
2025-06-21 13:04:58.202 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [tempdb] success
2025-06-21 13:04:58.202 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [kingdeedb] success
2025-06-21 13:04:58.202 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [kingdeedb]
2025-06-21 13:04:58.518 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.BomExportRow".
2025-06-21 13:04:58.524 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.BomExportRow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-21 13:04:58.601 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.ProductBomEntity".
2025-06-21 13:04:58.601 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-21 13:04:59.105 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@42d236fb
2025-06-21 13:04:59.238 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-21 13:04:59.268 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-21 13:04:59.283 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-21 13:04:59.291 [main] INFO  c.u.k.KingdeeDataApplication - Started KingdeeDataApplication in 4.897 seconds (process running for 6.434)
2025-06-21 13:06:19.704 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-21 13:06:21.709 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-21 13:06:21.713 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-21 13:06:21.715 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown initiated...
2025-06-21 13:06:21.723 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown completed.
2025-06-21 13:06:21.724 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [tempdb] success,
2025-06-21 13:06:21.724 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown initiated...
2025-06-21 13:06:21.728 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown completed.
2025-06-21 13:06:21.729 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [kingdeedb] success,
2025-06-21 13:06:21.729 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-21 22:26:39.581 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-21 22:26:39.647 [main] INFO  c.u.k.KingdeeDataApplication - Starting KingdeeDataApplication using Java 17.0.11 with PID 82917 (/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes started by stong_mac in /Volumes/diskD/work/jiulian/javacode/KingdeeData)
2025-06-21 22:26:39.648 [main] INFO  c.u.k.KingdeeDataApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-21 22:26:41.079 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-21 22:26:41.096 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-21 22:26:41.099 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 22:26:41.099 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-21 22:26:41.154 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 22:26:41.154 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1460 ms
2025-06-21 22:26:41.401 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Starting...
2025-06-21 22:26:41.777 [main] INFO  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Added connection ConnectionID:1 ClientConnectionId: 10d363f4-b1c3-4317-887e-d8793f377f15
2025-06-21 22:26:41.779 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Start completed.
2025-06-21 22:26:41.782 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Starting...
2025-06-21 22:26:41.948 [main] INFO  com.zaxxer.hikari.pool.HikariPool - tempdb - Added connection com.mysql.cj.jdbc.ConnectionImpl@70f85235
2025-06-21 22:26:41.948 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Start completed.
2025-06-21 22:26:41.948 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [tempdb] success
2025-06-21 22:26:41.948 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [kingdeedb] success
2025-06-21 22:26:41.948 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [kingdeedb]
2025-06-21 22:26:42.262 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.BomExportRow".
2025-06-21 22:26:42.268 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.BomExportRow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-21 22:26:42.326 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.ProductBomEntity".
2025-06-21 22:26:42.326 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-21 22:26:42.788 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3a43d133
2025-06-21 22:26:42.900 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-21 22:26:42.932 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-21 22:26:42.944 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-21 22:26:42.951 [main] INFO  c.u.k.KingdeeDataApplication - Started KingdeeDataApplication in 3.984 seconds (process running for 5.536)
2025-06-21 22:27:00.251 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 22:27:00.254 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 22:27:00.255 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-21 22:27:02.327 [http-nio-8080-exec-3] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 531 ms
2025-06-21 22:27:08.085 [http-nio-8080-exec-4] INFO  c.u.k.controller.BomDataController - 数据获取成功7404
