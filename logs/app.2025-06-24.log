2025-06-24 05:00:34.498 [Thread-1] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:232 ClientConnectionId: 8b2fe911-111e-4843-b043-19b7c60ab1aa Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:8b2fe911-111e-4843-b043-19b7c60ab1aa
2025-06-24 05:00:39.547 [Thread-1] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:232 ClientConnectionId: 32bfc6f0-d2a4-4399-8f06-dec29076aa64 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:32bfc6f0-d2a4-4399-8f06-dec29076aa64
2025-06-24 05:00:39.553 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:232 ClientConnectionId: 32bfc6f0-d2a4-4399-8f06-dec29076aa64 (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:00:44.558 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:238 ClientConnectionId: dfd8840e-de27-440e-89fb-71154ac9a693 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:dfd8840e-de27-440e-89fb-71154ac9a693
2025-06-24 05:00:49.562 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:238 ClientConnectionId: d5ea1c8c-d6df-4dde-8b30-6bbe97a2bb89 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:d5ea1c8c-d6df-4dde-8b30-6bbe97a2bb89
2025-06-24 05:00:52.123 [Thread-2] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:233 ClientConnectionId: 18cdcdae-ba8b-4060-bc6c-af449bc3ff2d Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:18cdcdae-ba8b-4060-bc6c-af449bc3ff2d
2025-06-24 05:00:54.577 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:239 ClientConnectionId: 1cfededb-cfe8-443d-b7e9-b930f49e8bd7 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:1cfededb-cfe8-443d-b7e9-b930f49e8bd7
2025-06-24 05:00:57.125 [Thread-2] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:233 ClientConnectionId: e9217fd0-535a-4215-a9ca-0a3993d9775b Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:e9217fd0-535a-4215-a9ca-0a3993d9775b
2025-06-24 05:00:57.125 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:233 ClientConnectionId: e9217fd0-535a-4215-a9ca-0a3993d9775b (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:00:59.581 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:239 ClientConnectionId: db5bc12e-feac-42ed-8a08-15c8d7a7d756 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:db5bc12e-feac-42ed-8a08-15c8d7a7d756
2025-06-24 05:01:02.127 [Thread-3] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:230 ClientConnectionId: b505a698-4e98-4830-9321-e7aa92bb3841 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:b505a698-4e98-4830-9321-e7aa92bb3841
2025-06-24 05:01:04.613 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:240 ClientConnectionId: 20aa7476-d3ec-45ac-b9eb-bc7306a71a22 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:20aa7476-d3ec-45ac-b9eb-bc7306a71a22
2025-06-24 05:01:07.131 [Thread-3] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:230 ClientConnectionId: 7dc401f3-e516-4ed3-8769-cf397cbd8b36 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:7dc401f3-e516-4ed3-8769-cf397cbd8b36
2025-06-24 05:01:07.131 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:230 ClientConnectionId: 7dc401f3-e516-4ed3-8769-cf397cbd8b36 (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:01:09.617 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:240 ClientConnectionId: 3384f5ac-86c7-491b-a1b7-cfbdadb81794 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:3384f5ac-86c7-491b-a1b7-cfbdadb81794
2025-06-24 05:01:12.133 [Thread-4] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:237 ClientConnectionId: 0a9c486d-e73b-468a-a329-16f042d09cbd Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:0a9c486d-e73b-468a-a329-16f042d09cbd
2025-06-24 05:01:14.670 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:241 ClientConnectionId: 91f138ce-ac99-4614-ba2c-43792e4448b8 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:91f138ce-ac99-4614-ba2c-43792e4448b8
2025-06-24 05:01:17.135 [Thread-4] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:237 ClientConnectionId: 2999df8a-3b5e-4d67-96c0-4ed0291ec2b1 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:2999df8a-3b5e-4d67-96c0-4ed0291ec2b1
2025-06-24 05:01:17.135 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:237 ClientConnectionId: 2999df8a-3b5e-4d67-96c0-4ed0291ec2b1 (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:01:19.674 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:241 ClientConnectionId: 4a30111b-27c1-4495-8317-676b5940f9e1 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:4a30111b-27c1-4495-8317-676b5940f9e1
2025-06-24 05:01:22.139 [Thread-5] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:235 ClientConnectionId: 13f0804e-1218-4208-84d6-33ebaf6c7af1 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:13f0804e-1218-4208-84d6-33ebaf6c7af1
2025-06-24 05:01:24.765 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:242 ClientConnectionId: 396f02a3-f86c-4c63-b9c8-63781b475a9f Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:396f02a3-f86c-4c63-b9c8-63781b475a9f
2025-06-24 05:01:27.141 [Thread-5] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:235 ClientConnectionId: 91c92899-0c48-405b-a8d0-d20ee845d7a6 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:91c92899-0c48-405b-a8d0-d20ee845d7a6
2025-06-24 05:01:27.141 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:235 ClientConnectionId: 91c92899-0c48-405b-a8d0-d20ee845d7a6 (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:01:29.767 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:242 ClientConnectionId: 3e85ebd1-85ac-4416-bf61-b06ed2a374f8 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:3e85ebd1-85ac-4416-bf61-b06ed2a374f8
2025-06-24 05:01:32.144 [Thread-6] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:229 ClientConnectionId: 4a5ed0b3-da16-47a6-ab75-603f865549f3 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:4a5ed0b3-da16-47a6-ab75-603f865549f3
2025-06-24 05:01:34.937 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:243 ClientConnectionId: 8e04ea69-21da-4165-8756-a25bd0d19272 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:8e04ea69-21da-4165-8756-a25bd0d19272
2025-06-24 05:01:37.147 [Thread-6] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:229 ClientConnectionId: de2610b4-1892-437b-90f9-1a59f5c35cad Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:de2610b4-1892-437b-90f9-1a59f5c35cad
2025-06-24 05:01:37.147 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:229 ClientConnectionId: de2610b4-1892-437b-90f9-1a59f5c35cad (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:01:37.148 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Thread starvation or clock leap detected (housekeeper delta=57s592ms).
2025-06-24 05:01:39.939 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:243 ClientConnectionId: 332bb693-c91d-4a53-bb70-ea647215f261 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:332bb693-c91d-4a53-bb70-ea647215f261
2025-06-24 05:01:42.153 [Thread-7] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:234 ClientConnectionId: b8b17de3-7aa4-4224-9bc1-9eeac1492064 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:b8b17de3-7aa4-4224-9bc1-9eeac1492064
2025-06-24 05:01:45.266 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:244 ClientConnectionId: 3b244d48-e2e5-43f9-9853-d7167e766e87 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:3b244d48-e2e5-43f9-9853-d7167e766e87
2025-06-24 05:01:47.156 [Thread-7] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:234 ClientConnectionId: e01b7676-09e9-4673-a4c6-fd26813547cd Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:e01b7676-09e9-4673-a4c6-fd26813547cd
2025-06-24 05:01:47.156 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:234 ClientConnectionId: e01b7676-09e9-4673-a4c6-fd26813547cd (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:01:50.270 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:244 ClientConnectionId: 6300da6c-0bac-4b8f-b86c-007f871eb060 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:6300da6c-0bac-4b8f-b86c-007f871eb060
2025-06-24 05:01:52.161 [Thread-8] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:236 ClientConnectionId: 1af71173-1911-4974-8f4e-03af6d554614 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:1af71173-1911-4974-8f4e-03af6d554614
2025-06-24 05:01:55.922 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:245 ClientConnectionId: abfd6afd-0c7d-43e6-b4c8-7e222a461375 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:abfd6afd-0c7d-43e6-b4c8-7e222a461375
2025-06-24 05:01:57.164 [Thread-8] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:236 ClientConnectionId: 03cbbc83-2c56-43fd-a6f7-1db27cd7102a Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:03cbbc83-2c56-43fd-a6f7-1db27cd7102a
2025-06-24 05:01:57.165 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:236 ClientConnectionId: 03cbbc83-2c56-43fd-a6f7-1db27cd7102a (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:02:00.925 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:245 ClientConnectionId: 48de9851-a95c-4805-a5d9-a169eee6d25e Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:48de9851-a95c-4805-a5d9-a169eee6d25e
2025-06-24 05:02:04.226 [Thread-9] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:231 ClientConnectionId: f444f647-566d-483c-aa1b-1ab641559312 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:f444f647-566d-483c-aa1b-1ab641559312
2025-06-24 05:02:07.215 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:246 ClientConnectionId: 7a0a2506-3c43-4db6-841d-bc6938e03c14 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:7a0a2506-3c43-4db6-841d-bc6938e03c14
2025-06-24 05:02:09.229 [Thread-9] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:231 ClientConnectionId: f62c635a-701d-40d0-bb16-61daa1bdb2fc Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:f62c635a-701d-40d0-bb16-61daa1bdb2fc
2025-06-24 05:02:09.231 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Failed to validate connection ConnectionID:231 ClientConnectionId: f62c635a-701d-40d0-bb16-61daa1bdb2fc (该连接已关闭。). Possibly consider using a shorter maxLifetime value.
2025-06-24 05:02:12.217 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:246 ClientConnectionId: e3032c45-409f-4085-b651-67774830dca3 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:e3032c45-409f-4085-b651-67774830dca3
2025-06-24 05:02:19.788 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:247 ClientConnectionId: a9dbc96e-3679-44e0-8e2e-0aabd7e20041 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:a9dbc96e-3679-44e0-8e2e-0aabd7e20041
2025-06-24 05:02:24.792 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:247 ClientConnectionId: 487b17c0-e041-4347-bebd-abc5e4ed44c5 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:487b17c0-e041-4347-bebd-abc5e4ed44c5
2025-06-24 05:02:24.792 [kingdeedb:connection-adder] WARN  com.zaxxer.hikari.pool.PoolBase - kingdeedb - Pool is empty, failed to create/setup connection (b23cd0d3-1bc5-4bef-ad33-60d8a73a1671)
com.microsoft.sqlserver.jdbc.SQLServerException: Connection reset ClientConnectionId:487b17c0-e041-4347-bebd-abc5e4ed44c5
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:4580)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2177)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.prelogin(SQLServerConnection.java:4290)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:4098)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:3690)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:3499)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:2207)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:1320)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:139)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:368)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:205)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:483)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:747)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:328)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at com.microsoft.sqlserver.jdbc.TDSChannel$ProxyInputStream.readInternal(IOBuffer.java:1206)
	at com.microsoft.sqlserver.jdbc.TDSChannel$ProxyInputStream.read(IOBuffer.java:1192)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2166)
	... 16 common frames omitted
2025-06-24 05:02:30.048 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:248 ClientConnectionId: f4d39840-b9f9-4ef8-b4f2-04ef88bc7236 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:f4d39840-b9f9-4ef8-b4f2-04ef88bc7236
2025-06-24 05:02:30.049 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:248 ClientConnectionId: f92fd006-ff6f-49d0-b836-c9341b921c8e Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:f92fd006-ff6f-49d0-b836-c9341b921c8e
2025-06-24 05:02:35.058 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:249 ClientConnectionId: a67b833a-b819-41bd-b003-5ac2a57d44af Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:a67b833a-b819-41bd-b003-5ac2a57d44af
2025-06-24 05:02:35.060 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:249 ClientConnectionId: 7a4fe6ee-4bd0-47f0-a241-320634f230b0 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:7a4fe6ee-4bd0-47f0-a241-320634f230b0
2025-06-24 05:02:45.070 [kingdeedb:connection-adder] WARN  c.m.s.j.i.SQLServerConnection - ConnectionID:250 ClientConnectionId: 0a383d39-c32d-4f80-8c63-0d13f70a8921 Prelogin error: host ************** port 1433 Error reading prelogin response: Connection reset ClientConnectionId:0a383d39-c32d-4f80-8c63-0d13f70a8921
2025-06-24 09:31:37.134 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-24 09:31:39.194 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-24 09:31:39.255 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-24 09:31:39.340 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown initiated...
2025-06-24 09:31:39.371 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown completed.
2025-06-24 09:31:39.371 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [tempdb] success,
2025-06-24 09:31:39.371 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown initiated...
2025-06-24 09:31:39.374 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown completed.
2025-06-24 09:31:39.375 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [kingdeedb] success,
2025-06-24 09:31:39.375 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-24 09:58:51.439 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-24 09:58:51.488 [main] INFO  c.u.k.KingdeeDataApplication - Starting KingdeeDataApplication using Java 17.0.11 with PID 2533 (/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes started by stong_mac in /Volumes/diskD/work/jiulian/javacode/KingdeeData)
2025-06-24 09:58:51.489 [main] INFO  c.u.k.KingdeeDataApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-24 09:58:52.628 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-24 09:58:52.641 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 09:58:52.643 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-24 09:58:52.644 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-24 09:58:52.693 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-24 09:58:52.694 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1166 ms
2025-06-24 09:58:52.867 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Starting...
2025-06-24 09:58:53.136 [main] INFO  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Added connection ConnectionID:1 ClientConnectionId: d1949ef3-b509-43dd-9263-c80001a05e2c
2025-06-24 09:58:53.138 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Start completed.
2025-06-24 09:58:53.141 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Starting...
2025-06-24 09:58:53.285 [main] INFO  com.zaxxer.hikari.pool.HikariPool - tempdb - Added connection com.mysql.cj.jdbc.ConnectionImpl@24aedcc5
2025-06-24 09:58:53.285 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Start completed.
2025-06-24 09:58:53.285 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [tempdb] success
2025-06-24 09:58:53.285 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [kingdeedb] success
2025-06-24 09:58:53.286 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [kingdeedb]
2025-06-24 09:58:53.533 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.BomExportRow".
2025-06-24 09:58:53.538 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.BomExportRow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-24 09:58:53.584 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.ProductBomEntity".
2025-06-24 09:58:53.584 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-24 09:58:53.970 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7da10b5b
2025-06-24 09:58:54.084 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-24 09:58:54.109 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 09:58:54.119 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-24 09:58:54.126 [main] INFO  c.u.k.KingdeeDataApplication - Started KingdeeDataApplication in 3.158 seconds (process running for 4.264)
2025-06-24 09:59:00.504 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 09:59:00.505 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-24 09:59:00.506 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-24 09:59:02.234 [http-nio-8080-exec-4] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 270 ms
2025-06-24 10:04:47.892 [http-nio-8080-exec-8] INFO  c.u.k.controller.BomDataController - 查询正查数据根据正查数据计算反差数据
2025-06-24 10:04:53.217 [http-nio-8080-exec-8] INFO  c.u.k.controller.BomDataController - 正查数据保存完成
2025-06-24 16:29:58.368 [kingdeedb:housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Retrograde clock change detected (housekeeper delta=29s851ms), soft-evicting connections from pool.
2025-06-24 16:29:58.370 [tempdb:housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - tempdb - Retrograde clock change detected (housekeeper delta=29s851ms), soft-evicting connections from pool.
2025-06-24 19:47:31.594 [http-nio-8080-exec-2] INFO  c.u.k.controller.BomDataController - 数据获取成功20
2025-06-24 19:47:31.618 [http-nio-8080-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: java.lang.RuntimeException: java.lang.IllegalArgumentException: argument "content" is null] with root cause
java.lang.IllegalArgumentException: argument "content" is null
	at com.fasterxml.jackson.databind.ObjectMapper._assertNotNull(ObjectMapper.java:5126)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3854)
	at com.unionman.kingdeedata.service.impl.BomDataServiceImpl.startReplaceDataByReady(BomDataServiceImpl.java:348)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor.invoke(DynamicDataSourceAnnotationInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.unionman.kingdeedata.service.impl.BomDataServiceImpl$$SpringCGLIB$$0.startReplaceDataByReady(<generated>)
	at com.unionman.kingdeedata.controller.BomDataController.startBomChangeData(BomDataController.java:174)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-24 19:48:04.116 [http-nio-8080-exec-4] INFO  c.u.k.controller.BomDataController - 数据获取成功20
2025-06-24 19:48:04.127 [http-nio-8080-exec-4] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: java.lang.RuntimeException: java.lang.IllegalArgumentException: argument "content" is null] with root cause
java.lang.IllegalArgumentException: argument "content" is null
	at com.fasterxml.jackson.databind.ObjectMapper._assertNotNull(ObjectMapper.java:5126)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3854)
	at com.unionman.kingdeedata.service.impl.BomDataServiceImpl.startReplaceDataByReady(BomDataServiceImpl.java:348)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor.invoke(DynamicDataSourceAnnotationInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.unionman.kingdeedata.service.impl.BomDataServiceImpl$$SpringCGLIB$$0.startReplaceDataByReady(<generated>)
	at com.unionman.kingdeedata.controller.BomDataController.startBomChangeData(BomDataController.java:174)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-24 19:48:23.941 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-24 19:48:24.004 [main] INFO  c.u.k.KingdeeDataApplication - Starting KingdeeDataApplication using Java 17.0.11 with PID 39715 (/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes started by stong_mac in /Volumes/diskD/work/jiulian/javacode/KingdeeData)
2025-06-24 19:48:24.005 [main] INFO  c.u.k.KingdeeDataApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-24 19:48:25.414 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-24 19:48:25.425 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-24 19:48:25.427 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-24 19:48:25.427 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-24 19:48:25.496 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-24 19:48:25.496 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1433 ms
2025-06-24 19:48:25.758 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Starting...
2025-06-24 19:48:26.157 [main] INFO  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Added connection ConnectionID:1 ClientConnectionId: 31d510be-08d7-48ce-81fd-2cc35cef320f
2025-06-24 19:48:26.159 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Start completed.
2025-06-24 19:48:26.162 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Starting...
2025-06-24 19:48:26.373 [main] INFO  com.zaxxer.hikari.pool.HikariPool - tempdb - Added connection com.mysql.cj.jdbc.ConnectionImpl@63dc3420
2025-06-24 19:48:26.374 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Start completed.
2025-06-24 19:48:26.374 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [tempdb] success
2025-06-24 19:48:26.374 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [kingdeedb] success
2025-06-24 19:48:26.374 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [kingdeedb]
2025-06-24 19:48:26.713 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.BomExportRow".
2025-06-24 19:48:26.719 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.BomExportRow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-24 19:48:26.815 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.ProductBomEntity".
2025-06-24 19:48:26.815 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-24 19:48:27.522 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@673bb956
2025-06-24 19:48:27.666 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-24 19:48:27.688 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-24 19:48:27.708 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-24 19:48:27.720 [main] INFO  c.u.k.KingdeeDataApplication - Started KingdeeDataApplication in 4.375 seconds (process running for 10.132)
2025-06-24 19:48:36.589 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-24 19:48:36.593 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-24 19:48:36.605 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 12 ms
2025-06-24 19:48:37.195 [http-nio-8080-exec-1] INFO  c.u.k.controller.BomDataController - 数据获取成功20
2025-06-24 19:48:45.178 [http-nio-8080-exec-1] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: java.lang.RuntimeException: java.lang.IllegalArgumentException: argument "content" is null] with root cause
java.lang.IllegalArgumentException: argument "content" is null
	at com.fasterxml.jackson.databind.ObjectMapper._assertNotNull(ObjectMapper.java:5126)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3854)
	at com.unionman.kingdeedata.service.impl.BomDataServiceImpl.startReplaceDataByReady(BomDataServiceImpl.java:348)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor.invoke(DynamicDataSourceAnnotationInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.unionman.kingdeedata.service.impl.BomDataServiceImpl$$SpringCGLIB$$0.startReplaceDataByReady(<generated>)
	at com.unionman.kingdeedata.controller.BomDataController.startBomChangeData(BomDataController.java:174)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:258)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:191)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-06-24 19:49:12.727 [http-nio-8080-exec-3] INFO  c.u.k.s.impl.BomDataServiceImpl - 金蝶所有物料数据：43805
2025-06-24 19:49:12.739 [http-nio-8080-exec-3] INFO  c.u.k.controller.BomDataController - huoqushuju 43805
2025-06-24 19:49:34.550 [http-nio-8080-exec-5] INFO  c.u.k.controller.BomDataController - 数据获取成功20
2025-06-24 19:54:18.563 [http-nio-8080-exec-9] INFO  c.u.k.controller.BomDataController - 查询正查数据根据正查数据计算反差数据
2025-06-24 19:54:18.736 [http-nio-8080-exec-9] INFO  c.u.k.controller.BomDataController - 正查数据保存完成
