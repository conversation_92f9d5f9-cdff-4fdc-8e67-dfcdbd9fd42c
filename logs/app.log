2025-06-28 13:52:06.299 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-28 13:52:06.388 [main] INFO  c.u.k.KingdeeDataApplication - Starting KingdeeDataApplication using Java 17.0.11 with PID 36628 (/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes started by stong_mac in /Volumes/diskD/work/jiulian/javacode/KingdeeData)
2025-06-28 13:52:06.389 [main] INFO  c.u.k.KingdeeDataApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-28 13:52:07.931 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-28 13:52:07.945 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-28 13:52:07.947 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-28 13:52:07.948 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-28 13:52:08.003 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-28 13:52:08.005 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1570 ms
2025-06-28 13:52:08.244 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Starting...
2025-06-28 13:52:08.702 [main] INFO  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Added connection ConnectionID:1 ClientConnectionId: df725fff-30b1-4215-a937-3edcd02f584b
2025-06-28 13:52:08.706 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Start completed.
2025-06-28 13:52:08.711 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Starting...
2025-06-28 13:52:08.931 [main] INFO  com.zaxxer.hikari.pool.HikariPool - tempdb - Added connection com.mysql.cj.jdbc.ConnectionImpl@1cc9bd9b
2025-06-28 13:52:08.932 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Start completed.
2025-06-28 13:52:08.932 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [tempdb] success
2025-06-28 13:52:08.933 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [kingdeedb] success
2025-06-28 13:52:08.933 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [kingdeedb]
2025-06-28 13:52:09.379 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.BomExportRow".
2025-06-28 13:52:09.390 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.BomExportRow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-28 13:52:09.495 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.ProductBomEntity".
2025-06-28 13:52:09.495 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-28 13:52:09.636 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name '/ procurementController' defined in file [/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes/com/unionman/kingdeedata/controller/ProcurementController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'procurementAdjustmentServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': No qualifying bean of type 'com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-06-28 13:52:09.637 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-28 13:52:09.641 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown initiated...
2025-06-28 13:52:09.654 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown completed.
2025-06-28 13:52:09.655 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [tempdb] success,
2025-06-28 13:52:09.655 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown initiated...
2025-06-28 13:52:09.663 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown completed.
2025-06-28 13:52:09.664 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [kingdeedb] success,
2025-06-28 13:52:09.664 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-28 13:52:09.669 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-28 13:52:09.688 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-28 13:52:09.730 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name '/ procurementController' defined in file [/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes/com/unionman/kingdeedata/controller/ProcurementController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'procurementAdjustmentServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': No qualifying bean of type 'com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.unionman.kingdeedata.KingdeeDataApplication.main(KingdeeDataApplication.java:11)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'procurementAdjustmentServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': No qualifying bean of type 'com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 21 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 36 common frames omitted
2025-06-28 13:54:13.506 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-28 13:54:13.565 [main] INFO  c.u.k.KingdeeDataApplication - Starting KingdeeDataApplication using Java 17.0.11 with PID 36678 (/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes started by stong_mac in /Volumes/diskD/work/jiulian/javacode/KingdeeData)
2025-06-28 13:54:13.566 [main] INFO  c.u.k.KingdeeDataApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-28 13:54:14.914 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-28 13:54:14.929 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-28 13:54:14.931 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-28 13:54:14.931 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-28 13:54:14.996 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-28 13:54:14.996 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1378 ms
2025-06-28 13:54:15.262 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Starting...
2025-06-28 13:54:15.537 [main] INFO  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Added connection ConnectionID:1 ClientConnectionId: 19a94ddd-8bb9-45ce-a121-b29a257360a3
2025-06-28 13:54:15.539 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Start completed.
2025-06-28 13:54:15.541 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Starting...
2025-06-28 13:54:15.714 [main] INFO  com.zaxxer.hikari.pool.HikariPool - tempdb - Added connection com.mysql.cj.jdbc.ConnectionImpl@10d98940
2025-06-28 13:54:15.714 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Start completed.
2025-06-28 13:54:15.714 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [tempdb] success
2025-06-28 13:54:15.714 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [kingdeedb] success
2025-06-28 13:54:15.715 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [kingdeedb]
2025-06-28 13:54:16.008 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.BomExportRow".
2025-06-28 13:54:16.014 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.BomExportRow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-28 13:54:16.065 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.ProductBomEntity".
2025-06-28 13:54:16.065 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-28 13:54:16.145 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name '/procurementController' defined in file [/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes/com/unionman/kingdeedata/controller/ProcurementController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'procurementAdjustmentServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': No qualifying bean of type 'com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-06-28 13:54:16.146 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-28 13:54:16.148 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown initiated...
2025-06-28 13:54:16.155 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown completed.
2025-06-28 13:54:16.155 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [tempdb] success,
2025-06-28 13:54:16.155 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown initiated...
2025-06-28 13:54:16.171 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown completed.
2025-06-28 13:54:16.171 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [kingdeedb] success,
2025-06-28 13:54:16.171 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-06-28 13:54:16.174 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-28 13:54:16.188 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-28 13:54:16.213 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name '/procurementController' defined in file [/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes/com/unionman/kingdeedata/controller/ProcurementController.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'procurementAdjustmentServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': No qualifying bean of type 'com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.unionman.kingdeedata.KingdeeDataApplication.main(KingdeeDataApplication.java:11)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'procurementAdjustmentServiceImpl': Unsatisfied dependency expressed through field 'baseMapper': No qualifying bean of type 'com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:788)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:768)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:146)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:509)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1459)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1740)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 21 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:785)
	... 36 common frames omitted
2025-06-28 13:55:14.169 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-28 13:55:14.235 [main] INFO  c.u.k.KingdeeDataApplication - Starting KingdeeDataApplication using Java 17.0.11 with PID 36707 (/Volumes/diskD/work/jiulian/javacode/KingdeeData/target/classes started by stong_mac in /Volumes/diskD/work/jiulian/javacode/KingdeeData)
2025-06-28 13:55:14.236 [main] INFO  c.u.k.KingdeeDataApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-28 13:55:15.569 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-28 13:55:15.584 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-28 13:55:15.586 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-28 13:55:15.586 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-28 13:55:15.652 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-28 13:55:15.652 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1355 ms
2025-06-28 13:55:15.830 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Starting...
2025-06-28 13:55:16.096 [main] INFO  com.zaxxer.hikari.pool.HikariPool - kingdeedb - Added connection ConnectionID:1 ClientConnectionId: 13afad26-c580-4a3f-a33e-b636315a7f32
2025-06-28 13:55:16.098 [main] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Start completed.
2025-06-28 13:55:16.101 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Starting...
2025-06-28 13:55:16.250 [main] INFO  com.zaxxer.hikari.pool.HikariPool - tempdb - Added connection com.mysql.cj.jdbc.ConnectionImpl@6233c6c2
2025-06-28 13:55:16.250 [main] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Start completed.
2025-06-28 13:55:16.250 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [tempdb] success
2025-06-28 13:55:16.250 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [kingdeedb] success
2025-06-28 13:55:16.250 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [kingdeedb]
2025-06-28 13:55:16.534 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.BomExportRow".
2025-06-28 13:55:16.539 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.BomExportRow ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-28 13:55:16.595 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.unionman.kingdeedata.excel.entity.ProductBomEntity".
2025-06-28 13:55:16.595 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-28 13:55:17.068 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@538cd0f2
2025-06-28 13:55:17.205 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.unionman.kingdeedata.excel.entity.ProductBomEntity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-28 13:55:17.243 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-28 13:55:17.258 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-28 13:55:17.269 [main] INFO  c.u.k.KingdeeDataApplication - Started KingdeeDataApplication in 3.611 seconds (process running for 4.444)
2025-06-28 13:55:30.404 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-28 13:55:32.411 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-28 13:55:32.415 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-28 13:55:32.417 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown initiated...
2025-06-28 13:55:32.425 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - tempdb - Shutdown completed.
2025-06-28 13:55:32.425 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [tempdb] success,
2025-06-28 13:55:32.426 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown initiated...
2025-06-28 13:55:32.430 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - kingdeedb - Shutdown completed.
2025-06-28 13:55:32.430 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [kingdeedb] success,
2025-06-28 13:55:32.431 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
