------====================== 操作前备份及还原 ==========================----
--备份及还原表 操作
-- 1. 创建备份表（结构 + 数据）
SELECT * INTO customer_backup_20250628 FROM customer;

-- 2. 修改原始数据（略）

-- 3. 需要还原时：
TRUNCATE TABLE customer;
INSERT INTO customer SELECT * FROM customer_backup_20250628;
------======================备份还原表end==========================----



--1、采购： 先采购申请 -> 采购订单 -> 采购收料 -> 采购入库

-- 采购申请  T_PUR_REQUISITION / 明细表 T_PUR_REQENTRY / 还有个关联表
-- 采购订单 T_PUR_POORDER    /明细表 T_PUR_POORDERENTRY 要注意下 关联表、变更表
-- 采购收料 T_PUR_RECEIVE /明细表 T_PUR_RECEIVEENTRY
-- 采购入库， 入库单的一种 T_STK_INSTOCK / 明细表 T_STK_INSTOCKENTRY ，注意下关联表



-- 采购申请单查询
select main.FBILLNO,entry.FENTRYID from T_PUR_REQUISITION main
left join T_PUR_REQENTRY entry on main.FID = entry.FID

where main.FBILLNO='' and entry.FMATERIALID= ;

-- 采购申请单明细调整

update T_PUR_REQENTRY set FMATERIALID= where FENTRYID=''


-- 采购订单查询
select main.FBILLNO,entry.FENTRYID from T_PUR_POORDER main
left join T_PUR_POORDERENTRY entry on main.FID = entry.FID
where main.FBILLNO='' and entry.FMATERIALID= ;
-- 采购订单明细调整
update T_PUR_POORDERENTRY set FMATERIALID= where FENTRYID=''

-- 采购收料单查询
select main.FBILLNO,entry.FENTRYID from T_PUR_RECEIVE main
left join T_PUR_RECEIVEENTRY entry on main.FID = entry.FID
where main.FBILLNO='' and entry.FMATERIALID= ;
-- 采购收料单明细调整
update T_PUR_RECEIVEENTRY set FMATERIALID= where FENTRYID='';

--采购入库单查询
select main.FBILLNO,entry.FENTRYID from T_STK_INSTOCK main
left join T_STK_INSTOCKENTRY entry on main.FID = entry.FID
where main.FBILLNO='' and entry.FMATERIALID= ;
-- 采购入库单明细调整
update T_STK_INSTOCKENTRY set FMATERIALID= where FENTRYID='';

-------------------------数据库备份----------------------------------
-- 采购相关表备份
-- 采购申请单、申请明细备份
SELECT * INTO T_PUR_REQUISITION_backup FROM T_PUR_REQUISITION;
SELECT * INTO T_PUR_REQENTRY_backup FROM T_PUR_REQENTRY;
-- 采购订单、明细备份
SELECT * INTO T_PUR_POORDER_backup FROM T_PUR_POORDER;
SELECT * INTO T_PUR_POORDERENTRY_backup FROM T_PUR_POORDERENTRY;

--采购收料单、明细备份
SELECT * INTO T_PUR_RECEIVE_backup FROM T_PUR_RECEIVE;
SELECT * INTO T_PUR_RECEIVEENTRY_backup FROM T_PUR_RECEIVEENTRY;

--采购入库单、明细备份
SELECT * INTO T_STK_INSTOCK_backup FROM T_STK_INSTOCK;
SELECT * INTO T_STK_INSTOCKENTRY_backup FROM T_STK_INSTOCKENTRY;
-------------------------数据库备份end----------------------------------





-------------------------其他出库----------------------------------
--2、其他出库
-- 出库申请单 T_STK_OUTSTOCKAPPLY / 明细表 T_STK_OUTSTOCKAPPLYENTRY
-- 其他出库单  T_STK_MISDELIVERY / 明细表 T_STK_MISDELIVERYENTRY

-- 出库申请单备份
SELECT * INTO T_STK_OUTSTOCKAPPLY_back FROM T_STK_OUTSTOCKAPPLY;
SELECT * INTO T_STK_OUTSTOCKAPPLYENTRY_back FROM T_STK_OUTSTOCKAPPLYENTRY;
-- 其他出库单备份
SELECT * INTO T_STK_MISDELIVERY_back FROM T_STK_MISDELIVERY;
SELECT * INTO T_STK_MISDELIVERYENTRY_back FROM T_STK_MISDELIVERYENTRY;

-- 出库申请单查询
select main.FBILLNO,entry.FENTRYID from T_STK_OUTSTOCKAPPLY main
                                            left join T_STK_OUTSTOCKAPPLYENTRY entry on main.FID = entry.FID
where main.FBILLNO='' and entry.FMATERIALID= ;

-- 出库申请单明细行调整
update T_STK_OUTSTOCKAPPLYENTRY set FMATERIALID= where FENTRYID in( )

-- 查询其他出库单
select main.FBILLNO,entry.FENTRYID from T_STK_MISDELIVERY main
                                            left join T_STK_MISDELIVERYENTRY entry on main.FID = entry.FID
where main.FBILLNO='' and entry.FMATERIALID= ;

-- 其他出库单明细调整
update T_STK_MISDELIVERYENTRY set FMATERIALID= where FENTRYID in( )

-------------------------其他出库end----------------------------------



------------------------ 销售订单-发货申请-销售出库 ----------------------------------
--3、销售订单-> 发货通知单 -> 销售出库
-- 销售订单T_SAL_ORDER  字段：F_JLKJ_JQLX
-- 销售发货通知单 （不知道是否是这个）T_SAL_DELIVERYNOTICE - 字段 F_JLKJ_JQLX
-- 销售出库单 T_SAL_OUTSTOCK -F_JLKJ_JQLX

-- 查询销售订单
select FID,FBILLNO from T_SAL_ORDER where FBILLNO=''
-- 根据 fid 更新机器类型
update T_SAL_ORDER set F_JLKJ_JQLX = '' where FID=
--查询发货通知单
select FID,FBILLNO from T_SAL_DELIVERYNOTICE where FBILLNO=''
--根据 fid 更新机器类型
update T_SAL_DELIVERYNOTICE set F_JLKJ_JQLX = '' where FID=''
-- 查询销售出库单
select FID,FBILLNO from T_SAL_OUTSTOCK where FBILLNO=''
-- 根据 fid 跟新机器类型
update T_SAL_OUTSTOCK set F_JLKJ_JQLX = '' where FID=''


-- 备份数据库
SELECT * INTO T_SAL_ORDER_back FROM T_SAL_ORDER;
SELECT * INTO T_SAL_DELIVERYNOTICE_back FROM T_SAL_DELIVERYNOTICE;
SELECT * INTO T_SAL_OUTSTOCK_back FROM T_SAL_OUTSTOCK;


------------------------ 销售订单-发货申请-销售出库end ----------------------------------




------------------------ 生产用料清单 ----------------------------------


------------------------ 生产用料清单end ----------------------------------



------------------------ 初始库存 (因2025年调整时间的问题，这部分数据要保留暂无需调整)----------------------------------
-- 5 初始库存(初始单据)

-- 初始库存表 T_STK_INVINIT /明细表 T_STK_INVINITDETAIL
select main.FBILLNO,entry.FENTRYID from T_STK_INVINIT main
                                            left join T_STK_INVINITDETAIL entry on main.FID = entry.FID
where main.FBILLNO='' and entry.FMATERIALID='' ;
-- 更新初始库存明细表
update T_STK_INVINITDETAIL set FMATERIALID =  where  FENTRYID in ();
-- 备份数据
select * into T_STK_INVINITDETAIL_back   from T_STK_INVINITDETAIL;

------------------------ 初始库存调整 end ----------------------------------





------------------------ 委外用料-委外领料 ----------------------------------

-- 委外用料 T_SUB_PPBOM 明细表  T_SUB_PPBOMENTRY
-- 委外领料单 T_SUB_PICKMTRL 明细表 T_SUB_PICKMTRLDATA

--备份数据库 委外用料
select * into T_SUB_PPBOM_back  from T_SUB_PPBOM;
select * into T_SUB_PPBOMENTRY_back  from T_SUB_PPBOMENTRY;
--领料单数据库备份
select * into T_SUB_PICKMTRL_back  from T_SUB_PICKMTRL;
select * into T_SUB_PICKMTRLDATA_back  from T_SUB_PICKMTRLDATA;

-- 数据还原脚本

TRUNCATE TABLE T_SUB_PPBOM;
TRUNCATE TABLE T_SUB_PPBOMENTRY;
TRUNCATE TABLE T_SUB_PICKMTRL;
TRUNCATE TABLE T_SUB_PICKMTRLDATA;

INSERT INTO T_SUB_PPBOM SELECT * FROM T_SUB_PPBOM_back;
INSERT INTO T_SUB_PPBOMENTRY SELECT * FROM T_SUB_PPBOMENTRY_back;
INSERT INTO T_SUB_PICKMTRL SELECT * FROM T_SUB_PICKMTRL_back;
INSERT INTO T_SUB_PICKMTRLDATA SELECT * FROM T_SUB_PICKMTRLDATA_back;




-- 根据物料编号检索，明细 id
select main.FBILLNO,entry.FENTRYID from T_SUB_PPBOM main
                                            left join T_SUB_PPBOMENTRY entry on main.FID = entry.FID
where main.FBILLNO='' and entry.FMATERIALID= ;
-- 更新明细表符合的物料信息
update T_SUB_PPBOMENTRY set FMATERIALID =  where  FENTRYID in ()

--跟据单号、物料编码查询 明细 id

select main.FBILLNO,entry.FENTRYID from T_SUB_PICKMTRL main
                                            left join T_SUB_PICKMTRLDATA entry on main.FID = entry.FID
where main.FBILLNO='' and entry.FMATERIALID= ;
--更新明细表
update T_SUB_PICKMTRLDATA set FMATERIALID =  where  FENTRYID in ();

------------------------ 委外用料-委外领料 end ----------------------------------