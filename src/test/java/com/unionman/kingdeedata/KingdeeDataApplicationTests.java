package com.unionman.kingdeedata;

import com.unionman.kingdeedata.excel.entity.BomExportRow;
import com.unionman.kingdeedata.service.BomDataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class KingdeeDataApplicationTests {

	@Autowired
	BomDataService bomDataService;

	@Test
	void contextLoads() {
		List< BomExportRow > rowData=new ArrayList<>();
		BomExportRow bomExportRow =new BomExportRow();
		bomExportRow.setFid(2128643);
		bomExportRow.setChipCode("10.0240.0326.99");
		bomExportRow.setChipSpec("Mstar方案芯片-MSO9385-AMZ-M01-ND5-BGA408-DVB-不带杜比-KG-RoHS-未承认");
		bomExportRow.setProductCode("17.0021.0055.99");
		bomExportRow.setProductSpec("数字机顶盒-高清双向(DVB-C)-1CMS85V100WE100-HE1机壳（HDC-2100S）-配(1.5MRF线/1.5MHDMI线)-说明书V3.0-加二维码贴纸-浙江大众版-不带杜比-HDMI-XBZ-AMZ主控-试产配置");

		rowData.add(bomExportRow);
		bomDataService.getBomListByExportRow (rowData);
	}




}
