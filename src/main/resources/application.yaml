spring:
  application:
    name: KingdeeData
  datasource:
    dynamic:
      primary: kingdeedb
      datasource:
        kingdeedb:
          url: ***************************************************************************************************************************;
          username: sa
          password: k3cloud@2022
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
        tempdb:
          url: ****************************************************************************************************************************************************************
          username: umoa
          password: unionman
          driver-class-name: com.mysql.cj.jdbc.Driver

server:
  port: 8080
# Swagger 配置
swagger:
  enabled: true

#logging:
#  level:
#    com.unionman.kingdeedata.mapper: debug
