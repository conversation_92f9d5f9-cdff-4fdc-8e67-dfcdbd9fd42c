spring:
  application:
    name: KingdeeData
  datasource:
    dynamic:
      primary: kingdeedb
      datasource:
        kingdeedb:
          url: ***************************************************************************************************************************;
          username: user_zhengzhenjin
          password: zzj@U003046
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
        tempdb:
          url: ****************************************************************************************************************************************************************
          username: umoa
          password: unionman
          driver-class-name: com.mysql.cj.jdbc.Driver

server:
  port: 8080

# Swagger 配置
swagger:
  enabled: true
