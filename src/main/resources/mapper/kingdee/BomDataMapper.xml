<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.unionman.kingdeedata.mapper.kingdee.BomDataMapper">

<!--    teb2.FID as FBOMID,teb2.FNUMBER,teb2.FMATERIALID,tbm2.FSPECIFICATION-->
    <resultMap id="BaseResultMap" type="com.unionman.kingdeedata.entity.BomData">
        <result column="NBOMID" property="nbomId" />
        <result column="NNUMBER" property="nnumber" />
        <result column="NMATERIALID" property="nmaterialId" />
        <result column="NSPECIFICATION" property="nspecification" />
        <result column="CFMATERIALID" property="cmaterialId" />
        <result column="CBOMID" property="cbomId" />
    </resultMap>


    <resultMap id="bomEntity" type="com.unionman.kingdeedata.entity.BomDateZC">
        <result column="FID" property="fid" />
        <result column="FBOMID" property="fbomId" />
        <result column="FNUMBER" property="fnumber" />
        <result column="FMATERIALID" property="fmaterialId" />
        <result column="FSPECIFICATION" property="fspecification" />
    </resultMap>


    <!-- 根据物料 ID 找上级    -->
    <select id="queryBomDataByMaterialID" resultMap="BaseResultMap"  parameterType="java.util.Set" >
        select distinct teb2.FID as NBOMID,teb2.FNUMBER as NNUMBER,teb2.FMATERIALID as NMATERIALID,tbm2.FSPECIFICATION as NSPECIFICATION ,teb.FMATERIALID as CFMATERIALID ,teb.FBOMID as CBOMID from
        T_ENG_BOMCHILD teb
        left join T_ENG_BOM teb2 on teb2.FID =teb.FID and teb2.FUSEORGID=1
        left join t_BD_Material_L tbm2 on tbm2.FMATERIALID = teb2.FMATERIALID
        where teb.FMATERIALID
        in
        <foreach item='materialid' collection='materialids' open='(' separator=',' close=')'>
            #{materialid}
        </foreach>
        AND (
        teb2.FNUMBER like '13%'
        or teb2.FNUMBER like '14%'
        or teb2.FNUMBER like '16.003%'
        or teb2.FNUMBER like '16.002%'
        or teb2.FNUMBER like '16.001%'
        or teb2.FNUMBER like '17%'
        )
    </select>

<!--    多版本的情况下 网上多一级都必须使用bomId 进行查询-->
<!--    <select id="queryBomDataByBomID" resultType="com.unionman.kingdeedata.entity.BomData" parameterType="java.util.List"  >-->
<!--        select  distinct tebf.FID as FBOMID,tebf.FNUMBER,tebf.FMATERIALID,tbm2.FSPECIFICATION,tebc.FBOMID as CFBOMID  from T_ENG_BOMCHILD  tebc-->
<!--                                                                                              left join T_ENG_BOM tebf  on tebf.FID =tebc.FID-->
<!--                                                                                              left join t_BD_Material_L tbm2 on tbm2.FMATERIALID = tebf.FMATERIALID-->
<!--        where tebc.FBOMID in-->
<!--        <foreach item='bomid' collection='fnumbebomIdsrs' open='(' separator=',' close=')'>-->
<!--            #{bomid}-->
<!--        </foreach>-->
<!--        AND  (-->
<!--        tebf.FNUMBER like '13%'-->
<!--        or tebf.FNUMBER like '14%'-->
<!--        or tebf.FNUMBER like '16.003%'-->
<!--        or tebf.FNUMBER like '16.002%'-->
<!--        or tebf.FNUMBER like '16.001%'-->
<!--        or tebf.FNUMBER like '17%'-->
<!--        )-->
<!--    </select>-->

    <select id="queryMaterialData" resultMap="BaseResultMap" parameterType="java.util.List">
        select 0 as NBOMID, tbm.FNUMBER as NNUMBER,tbm.FMATERIALID as NMATERIALID,tbml.FSPECIFICATION  as NSPECIFICATION,0 as CFMATERIALID,0 as CBOMID  from T_BD_MATERIAL tbm
        left join t_BD_Material_L tbml on tbm.FMATERIALID=tbml.FMATERIALID
        where tbm.FUSEORGID=1 and  tbm.FNUMBER
        in
        <foreach item='fnumber' collection='fnumbers' open='(' separator=',' close=')'>
            #{fnumber}
        </foreach>
    </select>

    <select id="queryBomChildByFatherBomIdBomTable" resultMap="BaseResultMap">
        SELECT ctbom.FID AS NBOMID,
        ctbom.FNUMBER AS NNUMBER,
        ctbom.FMATERIALID AS NMATERIALID,
        tbm2.FSPECIFICATION AS NSPECIFICATION,
        tbomc.FMATERIALID AS CFMATERIALID,
        tbomc.FID AS CBOMID
        FROM T_ENG_BOMCHILD tbomc
        LEFT JOIN T_ENG_BOM ctbom ON tbomc.FBOMID = ctbom.FID
        LEFT JOIN t_BD_Material_L tbm2 ON tbm2.FMATERIALID = ctbom.FMATERIALID
        WHERE tbomc.FID IN
        <foreach collection="bomIds" item="fid" open="(" separator="," close=")">
            #{fid}
        </foreach>
        AND ctbom.FNUMBER LIKE #{fnumber} and ctbom.FUSEORGID=1


    </select>

    <select id="queryBomChildByFatherBomIdMaterialTable" resultMap="BaseResultMap">
        -- 当来自物料
        SELECT 0 AS NBOMID,
        tma.FNUMBER AS NNUMBER,
        tma.FMATERIALID AS NMATERIALID,
        tbm2.FSPECIFICATION AS NSPECIFICATION,
        tbomc.FMATERIALID AS CFMATERIALID,
        tbomc.FID AS CBOMID
        FROM T_ENG_BOMCHILD tbomc
        LEFT JOIN T_BD_MATERIAL tma ON tma.FMATERIALID = tbomc.FMATERIALID and tma.FUSEORGID=1
        LEFT JOIN t_BD_Material_L tbm2 ON tbm2.FMATERIALID = tma.FMATERIALID
        WHERE tbomc.FID IN
        <foreach collection="bomIds" item="fid" open="(" separator="," close=")">
            #{fid}
        </foreach>
        AND tma.FNUMBER LIKE #{fnumber}  AND tbomc.FBOMID = 0
    </select>


     <!-- 查询子bom -->
    <select id="selectBomChildInfo" resultMap="bomEntity">
        SELECT
            tbom.FID,
            tbomc.FBOMID,
            tbom.FNUMBER,
            tbomc.FMATERIALID,
            tbm_l.FSPECIFICATION
        FROM T_ENG_BOMCHILD tbomc
                 LEFT JOIN T_BD_Material tbm ON tbomc.FMATERIALID = tbm.FMATERIALID
            -- 先查 配置bom
                 LEFT JOIN T_ENG_BOM tbom ON tbomc.FBOMID = tbom.FID
                 LEFT JOIN T_BD_MATERIAL_L tbm_l ON tbomc.FMATERIALID = tbm_l.FMATERIALID
--  查非配置bom 的情况
        WHERE tbomc.FID = #{bomid} and tbomc.FBOMID>0

        UNION ALL

        SELECT
            tbom_f.FID,
            tbom_f.FID AS FBOMID,
            tbm.FNUMBER AS FNUMBER,-- 直接物料 ID 即可
            tbm.FMATERIALID,
            tbm_l.FSPECIFICATION
        FROM T_ENG_BOMCHILD tbomc
                 LEFT JOIN T_BD_Material tbm ON tbomc.FMATERIALID = tbm.FMATERIALID
                 left  join t_eng_bom tbom_f  on tbm.FMATERIALID=tbom_f.FMATERIALID
                 LEFT JOIN T_BD_MATERIAL_L tbm_l ON tbomc.FMATERIALID = tbm_l.FMATERIALID
        WHERE tbomc.FID =  #{bomid} and tbomc.FBOMID=0  and ( tbom_f.FID is null or(  tbom_f.F_JLKJ_CHECKBOX=0 and tbom_f.FNUMBER NOT LIKE '%[_]%') )

    </select>

    <!--  物料替换：查询所有物料    -->
    <select id="selectAllMaterial" resultMap="bomEntity">
        select FNUMBER,FMATERIALID from T_BD_Material where  FUSEORGID=1;
    </select>

    <!-- 验证 bom 单是否存在，并合理 size 要能对得上-->
    <select id="queryBomDataByFnumberAndChildMaterialId" resultType="com.unionman.kingdeedata.entity.BomChild"  >
        select bomchild.FENTRYID,bomchild.FMATERIALID
        from T_ENG_BOM bom
        left join T_ENG_BOMCHILD bomchild on bomchild.Fid = bom.Fid
        where bom.FNUMBER = #{bnumber}
        and bom.FUSEORGID = 1
        and bomchild.FMATERIALID IN
        <foreach item="materialId" collection="materialIds" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </select>

    <!-- bomchild 的 entryId删除  -->
    <delete id="deleteBomChildByEntryId">
        delete from T_ENG_BOMCHILD where FENTRYID in
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </delete>

    <!--    根据 bomchild 的 entryId 更新物料 ID-->
    <update id="updateBomChildMaterialIdByEntryId">
        update T_ENG_BOMCHILD set FMATERIALID = #{materialId} where FENTRYID = #{entryId}
    </update>


<!--    查询物料描述表-->
    <select id="queryMaterialDescription" resultType="com.unionman.kingdeedata.entity.MaterialLEntity">
            select FPKID,FNAME,FSPECIFICATION from T_BD_MATERIAL_L
    </select>


    <!--根据 fpkid 更新描述-->
    <update id="updateMaterialDescription">
        update T_BD_MATERIAL_L set FSPECIFICATION = #{specification} where FPKID = #{fpkid}
    </update>



</mapper>