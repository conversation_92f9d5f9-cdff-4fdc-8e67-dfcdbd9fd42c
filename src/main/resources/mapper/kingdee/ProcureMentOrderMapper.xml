<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.unionman.kingdeedata.mapper.kingdee.ProcurementOrderMapper">

     <!-- 基础数据，只需要单号+ 明细行 ID-->
    <resultMap id="ProcureMentBase" type="com.unionman.kingdeedata.entity.BaseMainEntryIdEntity">
        <result column="FBILLNO" property="fbillno" />
        <result column="FENTRYID" property="fentryid" />
    </resultMap>

    <!-- 查询采购申请单 -->
    <select id="queryPurchaseRequisition" resultMap="ProcureMentBase">
        SELECT main.FBILLNO, entry.FENTRYID
        FROM T_PUR_REQUISITION main
                 LEFT JOIN T_PUR_REQENTRY entry ON main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 更新采购申请单明细 -->
    <update id="updatePurchaseRequisitionEntry">
        UPDATE T_PUR_REQENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>


    <!-- 查询采购订单 -->
    <select id="queryPurchaseOrder" resultMap="ProcureMentBase">
        SELECT main.FBILLNO, entry.FENTRYID
        FROM T_PUR_POORDER main
                 LEFT JOIN T_PUR_POORDERENTRY entry ON main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 更新采购订单明细 -->
    <update id="updatePurchaseOrderEntry">
        UPDATE T_PUR_POORDERENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>


    <!-- 查询采购收料单 -->
    <select id="queryPurchaseReceive" resultMap="ProcureMentBase">
        SELECT main.FBILLNO, entry.FENTRYID
        FROM T_PUR_RECEIVE main
                 LEFT JOIN T_PUR_RECEIVEENTRY entry ON main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 更新采购收料单明细 -->
    <update id="updatePurchaseReceiveEntry">
        UPDATE T_PUR_RECEIVEENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>


    <!-- 查询采购入库单 -->
    <select id="queryPurchaseInStock" resultMap="ProcureMentBase">
        SELECT main.FBILLNO, entry.FENTRYID
        FROM T_STK_INSTOCK main
                 LEFT JOIN T_STK_INSTOCKENTRY entry ON main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 更新采购入库单明细 -->
    <update id="updatePurchaseInStockEntry">
        UPDATE T_STK_INSTOCKENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>

    <!-- 采购退料查询 -->
    <select id="queryPurchaseBackStock" resultMap="ProcureMentBase">
        select main.FBILLNO,entry.FENTRYID from T_PUR_MRB main
                    left join T_PUR_MRBENTRY entry on main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>
    <!-- 采购退料明细 -->
    <update id="updatePurchaseBackStockEntry">
        UPDATE T_PUR_MRBENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>





</mapper>