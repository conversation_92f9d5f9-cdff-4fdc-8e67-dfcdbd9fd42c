<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.unionman.kingdeedata.mapper.kingdee.ProductMaterialMapper">

     <!-- 基础数据，只需要单号+ 明细行 ID-->
    <resultMap id="ProductMaterialBase" type="com.unionman.kingdeedata.entity.BaseMainEntryIdEntity">
        <result column="FBILLNO" property="fbillno" />
        <result column="FENTRYID" property="fentryid" />
    </resultMap>

    <!-- 查询生产用料单明细 数据 -->
    <select id="queryProductMaterial" resultMap="ProductMaterialBase">
        select main.FBILLNO,entry.FENTRYID from T_PRD_PPBOM main
                    left join T_PRD_PPBOMENTRY entry on main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 更新生产用料单明细 -->
    <update id="updateProductMaterialEntry">
        UPDATE T_PRD_PPBOMENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>



    <!-- 查询生产用料单明细 数据 -->
    <select id="queryProductPickingMaterial" resultMap="ProductMaterialBase">
        select main.FBILLNO,entry.FENTRYID from T_PRD_PICKMTRL main
                                                    left join T_PRD_PICKMTRLDATA entry on main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 更新生产用料单明细 -->
    <update id="updateProductPickingMaterialEntry">
        UPDATE T_PRD_PICKMTRLDATA
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>



    <!-- 查询生产退料单 数据 -->
    <select id="queryProductBackMaterial" resultMap="ProductMaterialBase">
        select main.FBILLNO,entry.FENTRYID from T_PRD_RETURNMTRL main
           left join T_PRD_RETURNMTRLENTRY entry on main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 更新生产退料明细 -->
    <update id="updateProductBackMaterialEntry">
        UPDATE T_PRD_RETURNMTRLENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>



</mapper>