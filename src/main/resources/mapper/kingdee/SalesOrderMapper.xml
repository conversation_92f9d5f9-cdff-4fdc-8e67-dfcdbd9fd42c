<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.unionman.kingdeedata.mapper.kingdee.SalesOrderMapper">

    <!-- 基础数据，只需要单号和ID -->
    <resultMap id="SalesOrderBase" type="com.unionman.kingdeedata.entity.BaseMainEntryIdEntity">
        <result column="FID" property="fid" />
        <result column="FBILLNO" property="fbillno" />
    </resultMap>

    <!-- 查询销售订单 -->
    <select id="querySalesOrder" resultMap="SalesOrderBase">
        SELECT FID, FBILLNO 
        FROM T_SAL_ORDER 
        WHERE FBILLNO = #{billNo}
    </select>

    <!-- 更新销售订单机器类型 -->
    <update id="updateSalesOrderMachineType">
        UPDATE T_SAL_ORDER 
        SET F_JLKJ_JQLX = #{machineType} 
        WHERE FID = #{fid}
    </update>

    <!-- 查询发货通知单 -->
    <select id="queryDeliveryNotice" resultMap="SalesOrderBase">
        SELECT FID, FBILLNO 
        FROM T_SAL_DELIVERYNOTICE 
        WHERE FBILLNO = #{billNo}
    </select>

    <!-- 更新发货通知单机器类型 -->
    <update id="updateDeliveryNoticeMachineType">
        UPDATE T_SAL_DELIVERYNOTICE 
        SET F_JLKJ_JQLX = #{machineType} 
        WHERE FID = #{fid}
    </update>

    <!-- 查询销售出库单 -->
    <select id="querySalesOutstock" resultMap="SalesOrderBase">
        SELECT FID, FBILLNO 
        FROM T_SAL_OUTSTOCK 
        WHERE FBILLNO = #{billNo}
    </select>

    <!-- 更新销售出库单机器类型 -->
    <update id="updateSalesOutstockMachineType">
        UPDATE T_SAL_OUTSTOCK 
        SET F_JLKJ_JQLX = #{machineType} 
        WHERE FID = #{fid}
    </update>

     <!--根据物料编号查询销售出库单明细id-->
    <select id="querySalesOutstockEntryId" resultMap="SalesOrderBase">
        select main.FBILLNO, entry.FENTRYID
        from T_SAL_OUTSTOCK main
                 left join T_SAL_OUTSTOCKENTRY entry on main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo}
          AND entry.FMATERIALID = #{materialId}
    </select>
     <!-- 跟新销售出库单明细物料    -->
    <update id="updateSalesOutstockEntry">
            UPDATE T_SAL_OUTSTOCKENTRY
            SET FMATERIALID = #{targetMaterialId}
            WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
        #{entryId}
        </foreach>
    </update>
</mapper>