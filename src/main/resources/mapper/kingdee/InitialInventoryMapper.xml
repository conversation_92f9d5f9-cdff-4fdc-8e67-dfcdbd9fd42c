<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.unionman.kingdeedata.mapper.kingdee.InitialInventoryMapper">

    <!-- 基础数据，只需要单号+ 明细行 ID-->
    <resultMap id="InitialEntityBase" type="com.unionman.kingdeedata.entity.BaseMainEntryIdEntity">
        <result column="FBILLNO" property="fbillno" />
        <result column="FENTRYID" property="fentryid" />
    </resultMap>

    <!-- 查询生产用料单明细 数据 -->
    <select id="queryInitialInventoryMaterial" resultMap="InitialEntityBase">
        select main.FBILLNO,entry.FENTRYID from T_STK_INVINIT main
                                                    left join T_STK_INVINITDETAIL entry on main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 更新生产用料单明细 -->
    <update id="updateInitialInventoryEntry">
        UPDATE T_STK_INVINITDETAIL
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>




</mapper>