<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.unionman.kingdeedata.mapper.kingdee.OutbundMapper">

     <!-- 基础数据，只需要单号+ 明细行 ID-->
    <resultMap id="BaseMainEntry" type="com.unionman.kingdeedata.entity.BaseMainEntryIdEntity">
        <result column="FBILLNO" property="fbillno" />
        <result column="FENTRYID" property="fentryid" />
    </resultMap>

    <!-- 出库申请单查询 -->
    <select id="queryOutStockApply" resultMap="BaseMainEntry">
        SELECT main.FBILLNO, entry.FENTRYID
        FROM T_STK_OUTSTOCKAPPLY main
                 LEFT JOIN T_STK_OUTSTOCKAPPLYENTRY entry ON main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 出库申请单明细调整 -->
    <update id="updateOutStockApplyEntry">
        UPDATE T_STK_OUTSTOCKAPPLYENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>

    <!-- 其他出库单查询 -->
    <select id="queryOtherOutStock" resultMap="BaseMainEntry">
        SELECT main.FBILLNO, entry.FENTRYID
        FROM T_STK_MISDELIVERY main
                 LEFT JOIN T_STK_MISDELIVERYENTRY entry ON main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 其他出库单明细调整 -->
    <update id="updateOtherOutStockEntry">
        UPDATE T_STK_MISDELIVERYENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>


</mapper>