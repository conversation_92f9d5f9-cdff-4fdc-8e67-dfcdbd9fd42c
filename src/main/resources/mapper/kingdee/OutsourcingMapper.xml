<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.unionman.kingdeedata.mapper.kingdee.OutsourcingMapper">

    <!--委外用料单调整-->
    <resultMap id="OutsourcingBase" type="com.unionman.kingdeedata.entity.BaseMainEntryIdEntity">
        <result column="FID" property="fid" />
        <result column="FBILLNO" property="fbillno" />
    </resultMap>



    <!-- 委外用料明细查询 -->
    <select id="queryOutsourcingMaterial" resultMap="OutsourcingBase">
        select main.FBILLNO,entry.FENTRYID from T_SUB_PPBOM main
                                                    left join T_SUB_PPBOMENTRY entry on main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 委外用料更新 -->
    <update id="updateOutsourcingEntry">
        UPDATE T_SUB_PPBOMENTRY
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>


    <!-- 委外领料明细查询 -->
    <select id="queryOutsourcingPack" resultMap="OutsourcingBase">
        select main.FBILLNO,entry.FENTRYID from T_SUB_PICKMTRL main
                                                    left join T_SUB_PICKMTRLDATA entry on main.FID = entry.FID
        WHERE main.FBILLNO = #{billNo} AND entry.FMATERIALID = #{materialId}
    </select>

    <!-- 委外用料更新 -->
    <update id="updateOutsourcingPickEntry">
        UPDATE T_SUB_PICKMTRLDATA
        SET FMATERIALID = #{targetMaterialId}
        WHERE FENTRYID IN
        <foreach item="entryId" collection="entryIds" open="(" separator="," close=")">
            #{entryId}
        </foreach>
    </update>

</mapper>