package com.unionman.kingdeedata.service;

import com.unionman.kingdeedata.entitytmp.OutboundAdjustmentRecordEntity;

public interface OutboundServiceKD {
    /**
     * 传入行数据 查询需要调整的数据
     */
    public void queryOutboundEntryID(OutboundAdjustmentRecordEntity outboundAdjustmentRecordEntity) ;

    /** 更新出库申请
     * @param outboundAdjustmentRecordEntity
     */
    public void updateOutStockApplyEntry(OutboundAdjustmentRecordEntity outboundAdjustmentRecordEntity) ;
    /** 更新出库单
     * @param outboundAdjustmentRecordEntity
     */
    public void updateOutStockEntry(OutboundAdjustmentRecordEntity outboundAdjustmentRecordEntity) ;
}
