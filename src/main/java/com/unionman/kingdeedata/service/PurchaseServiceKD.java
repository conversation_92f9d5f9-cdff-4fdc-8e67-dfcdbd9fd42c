package com.unionman.kingdeedata.service;

import com.unionman.kingdeedata.entitytmp.ProcurementAdjustmentRecordEntity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 金蝶 采购相关查询
 */
public interface PurchaseServiceKD {

    /**
     * 传入 每行需要调整的数据，进行操作
     */
    void queryPurchaseEntryID( ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity);


    /**
     *  更新申请单
     * @param procurementAdjustmentRecordEntity
     */
    void updatePurchaseRequisitionEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity);
    /**
     *  更新订单
     * @param procurementAdjustmentRecordEntity
     */
    void updatePurchaseOrderEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity);
    /**
     *  更新收料单
     * @param procurementAdjustmentRecordEntity
     */
    void updatePurchaseReceiveEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity);
    /**
     *  更新入库单
     * @param procurementAdjustmentRecordEntity
     */
    void updatePurchaseInStockEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity);

    /**
     * 采购退料单
     * @param procurementAdjustmentRecordEntity
     */
    void queryPurchaseBackStockEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity);
}
