package com.unionman.kingdeedata.service;

import com.unionman.kingdeedata.entity.BomData;
import com.unionman.kingdeedata.entity.BomDateZC;
import com.unionman.kingdeedata.excel.entity.BomExportRow;
import com.unionman.kingdeedata.excel.entity.BomMaterialReplaceEntity;
import com.unionman.kingdeedata.excel.entity.ProductBomEntity;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * 金蝶数据
 */
public interface BomDataService {
    // 获取bom 树数据
    List<BomData> queryBomDataTree();

    /**
     * 根据物料 ID 查询bom数据
     */
    List<BomData> queryBomDataByMaterialID(Set<Integer> materialIds);

    /**
     * 获取第一层物料数据
     * @return
     */
    public List<BomData> queryMaterialData();
    /**
     *  迭代获取后续bom 数据
      */
    Map<Integer,List<BomData>> queryBomDataByMaterialIds(Set<Integer> materialIds);


    List<ProductBomEntity> getBomListByExportRow (List<BomExportRow> rowData);



    Map<String,Integer> queryAllMaterialData();

    void startReplaceDataByReady(List<BomMaterialReplaceEntity> materialAndChangeMaterids);

    // 查询并调整物料描述表
    void queryAndChangeMaterialDescription();
}
