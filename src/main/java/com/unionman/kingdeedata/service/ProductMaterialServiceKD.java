package com.unionman.kingdeedata.service;

import com.unionman.kingdeedata.entitytmp.ProcurementAdjustmentRecordEntity;
import com.unionman.kingdeedata.entitytmp.ProductMaterialAdjustmentEntity;

/**
 * 生产用料单数据查询及调整 服务
 */
public interface ProductMaterialServiceKD {

    /**
     * 传入 每行需要调整的数据，进行操作
     */
    void queryProductMaterialEntryID( ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity);

    /**
     * 更新生产用料单数据
     */
    void updateProductMaterialEntry(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity);

    /**
     * 传入 每行需要调整的数据，进行操作
     */
    void queryProductPickingMaterialEntryID( ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity);

    /**
     * 更新生产领料单数据
     */
    void updateProductPickingMaterialEntry(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity);


    /**
     * 传入 每行需要调整的数据，进行操作
     */
    void queryProductBackMaterialEntryID( ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity);

    /**
     * 更新生产退料单数据
     */
    void updateProductBackMaterialEntry(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity);

}
