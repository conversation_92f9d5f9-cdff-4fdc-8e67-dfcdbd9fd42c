package com.unionman.kingdeedata.service;

import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import com.unionman.kingdeedata.entitytmp.OutsourcingMaterialAdjustmentEntity;

import java.util.List;

/**
 * 委外相关业务接口
 * <AUTHOR>
 */
public interface OutsourcingService {
    
    /**
     * 查询委外用料明细
     * @return 匹配的记录列表
     */
    void queryOutsourcingMaterialAndPick(OutsourcingMaterialAdjustmentEntity outsourcingMaterialAdjustmentEntity);
    
    /**
     * 更新委外用料条目
     */
    void updateOutsourcingEntry(OutsourcingMaterialAdjustmentEntity outsourcingMaterialAdjustmentEntity);
    

    /**
     * 更新委外领料条目
     */
    void updateOutsourcingPickEntry(OutsourcingMaterialAdjustmentEntity outsourcingMaterialAdjustmentEntity);
}
