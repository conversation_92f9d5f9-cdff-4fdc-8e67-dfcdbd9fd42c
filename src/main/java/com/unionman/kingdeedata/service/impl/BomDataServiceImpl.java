package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unionman.kingdeedata.entity.BomChild;
import com.unionman.kingdeedata.entity.BomData;
import com.unionman.kingdeedata.entity.BomDateZC;
import com.unionman.kingdeedata.entity.MaterialLEntity;
import com.unionman.kingdeedata.excel.entity.BomExportRow;
import com.unionman.kingdeedata.excel.entity.BomMaterialReplaceEntity;
import com.unionman.kingdeedata.excel.entity.ProductBomEntity;
import com.unionman.kingdeedata.mapper.kingdee.BomDataMapper;
import com.unionman.kingdeedata.service.BomDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static javax.swing.UIManager.get;

/**
 * <AUTHOR>
 * bom数据服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
@DS("kingdeedb")
public class BomDataServiceImpl implements BomDataService {

    private final BomDataMapper bomDataMapper;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<BomData> queryBomDataTree() {

        // 先获取物料数据
        List<BomData> materialData = queryMaterialData();
        Set<Integer> materialIds = materialData.stream().map(BomData::getNmaterialId).collect(Collectors.toSet());

        // 根据物料查询 贴片数据
        //List<BomData> tpBom = queryBomDataByMaterialID(materialIds);
        //Set<Integer> tpBomIds= tpBom.stream().map(BomData::getNmaterialId).collect(Collectors.toSet());

        // 直接从贴片开始查
        Map<Integer, List<BomData>> fatherBomMap = queryBomDataByMaterialIds(materialIds);
//        tpBom.forEach(bomData -> {
//
//            if (fatherBomMap.containsKey(bomData.getFbomId())) {
//                bomData.setFatherBomData(fatherBomMap.get(bomData.getFbomId()));
//            }
//            if (bomFunmberData.containsKey(bomData.getCfbomId())) {
//                List<BomData> listMapByBomF = bomFunmberData.get(bomData.getCfbomId());
//                listMapByBomF.add(bomData);
//            } else {
//                List<BomData> listMapByBomF = new ArrayList<>();
//                listMapByBomF.add(bomData);
//                bomFunmberData.put(bomData.getCfbomId(), listMapByBomF);
//            }
//        });

        // 再将物料数据进行封装
        materialData.forEach(materiaoNode -> {
            List<BomData> fatherBomDates = materiaoNode.getFatherBomData();
            if (fatherBomDates == null) {
                fatherBomDates = new ArrayList<>();
                materiaoNode.setFatherBomData(fatherBomDates);
            }
            if (fatherBomMap.containsKey(materiaoNode.getNmaterialId())) {

                fatherBomDates.addAll(fatherBomMap.get(materiaoNode.getNmaterialId()));
            }
        });
        return materialData;
    }

    @Override
    public List<BomData> queryBomDataByMaterialID(Set<Integer> materialIds) {
        return bomDataMapper.queryBomDataByMaterialID(materialIds);
    }

    @Override
    public List<BomData> queryMaterialData() {
        List<String> fnumbers = Arrays.asList("10.0240.0013.01", "10.0240.0021.99", "10.0240.0044.99", "10.0240.0045.99", "10.0240.0047.99", "10.0240.0048.99", "10.0240.0088.99", "10.0240.0095.99", "10.0240.0105.99", "10.0240.0109.99", "10.0240.0134.99", "10.0240.0235.01", "10.0240.0298.99", "10.0240.0304.99", "10.0240.0317.99", "10.0240.0318.99", "10.0240.0326.99", "10.0240.0341.99", "10.0240.0347.99", "10.0240.0297.01");
        return bomDataMapper.queryMaterialData(fnumbers);
    }

    @Override
    public Map<Integer, List<BomData>> queryBomDataByMaterialIds(Set<Integer> materialIds) {

        Map<Integer, List<BomData>> bomDataMap = new HashMap<>();
        if (!materialIds.isEmpty()) {
            List<BomData> queryBomList = bomDataMapper.queryBomDataByMaterialID(materialIds);
            if (!queryBomList.isEmpty()) {
                // 遍历queryBomList 获得 BomData 物料链表
                Set<Integer> materids = queryBomList.stream().map(BomData::getNmaterialId).collect(Collectors.toSet());

                Set<Integer> bomids = queryBomList.stream().map(BomData::getNbomId).collect(Collectors.toSet());


                // 默认查询 16002
                String queryFnumber = "16.002%";
                if (queryBomList.get(0).getNnumber().startsWith("17")) {
                    queryFnumber = "16.001%";
                }


                // 查询所有父节点下符合条件的子物料信息
                List<BomData> childBomListBomTables = bomDataMapper.queryBomChildByFatherBomIdBomTable(bomids, queryFnumber);
                List<BomData> childBomListMaterialTables = bomDataMapper.queryBomChildByFatherBomIdMaterialTable(bomids, queryFnumber);
                childBomListMaterialTables.addAll(childBomListBomTables);
                Map<Integer, BomData> cbomMap = childBomListMaterialTables.stream()
                        .filter(bom -> bom.getCbomId() != null)
                        .collect(Collectors.toMap(
                                BomData::getCbomId,
                                bom -> bom
                        ));

                Map<Integer, List<BomData>> fatherBomMap = queryBomDataByMaterialIds(materids);


                queryBomList.forEach(cBomData -> {
                    if (cbomMap.containsKey(cBomData.getNbomId())) {
                        cBomData.setChildBomData(cbomMap.get(cBomData.getNbomId()));
                    }

                    Integer putRelate = cBomData.getCbomId();// 用于压缩当前id ,方便子节点查找
                    Integer nBomid = cBomData.getNbomId();// 用于查找父节点中的明细x
                    Integer nMaterialId = cBomData.getNmaterialId();
//                if(cBomData.getNnumber().equals("17.0012.0071.99")){
//                    log.info("目标");
//                }
                    if (cBomData.getCbomId() <= 0) {
                        // 说明是没有带version 的版本，此时应该通过，物料编号来关联
                        putRelate = cBomData.getCmaterialId();
                    }
                    List<BomData> fatherBomDates = new ArrayList<>();
                    // 存在带版本的则有限存储带版本的，否则存储不带版本的
                    if (fatherBomMap.containsKey(nBomid)) {
                        fatherBomDates.addAll(fatherBomMap.get(nBomid));
                    } else if (fatherBomMap.containsKey(nMaterialId)) {
                        fatherBomDates.addAll(fatherBomMap.get(nMaterialId));
                    }
                    cBomData.setFatherBomData(fatherBomDates);

                    if (bomDataMap.containsKey(putRelate)) {
                        List<BomData> listMapByBomF = bomDataMap.get(putRelate);
                        listMapByBomF.add(cBomData);
                    } else {
                        List<BomData> listMapByBomF = new ArrayList<>();
                        bomDataMap.put(putRelate, listMapByBomF);
                        listMapByBomF.add(cBomData);
                    }
                });
            }
        }
        return bomDataMap;
    }

    /**
     * 传入 rowdata 生成 bom数据
     */

    @Override
    public List<ProductBomEntity> getBomListByExportRow(List<BomExportRow> rowData) {
        List<ProductBomEntity> result = new ArrayList<>();
        for (BomExportRow row : rowData) {
            if (row.getFid() != null) {
                // log.info("开始执行{}",row.getFid());
                BomDateZC parentBomEntity = new BomDateZC();
                ProductBomEntity productBomEntity = new ProductBomEntity();
                parentBomEntity.setFid(row.getFid());
                //产成品的 bom编码与 ，主芯片的内容都提前预设进去
                productBomEntity.setProductBomCode(row.getProductCode());
                productBomEntity.setFinishedProductSpec(row.getProductSpec());
                // 不绑定方便后续验证
//                productBomEntity.setChipCode(row.getChipCode());
//                productBomEntity.setChipSpec(row.getChipSpec());
                // 其他的内容从 查询子bom 的时候逐层去获取(是否保留这个结果都不重要）
                List<BomDateZC> bomChild = execQueryBomChildData(parentBomEntity, productBomEntity);
//                if (parentBomEntity.getFid() ==2128643) {
//                    System.out.println("dfdffdf");
//                }
                result.add(productBomEntity);
            }

        }
        return result;
    }

    public List<BomDateZC> execQueryBomChildData(BomDateZC parentBom, ProductBomEntity productBomEntity) {
//        if(parentBom.getFid()==422711){
//            System.out.println("target");
//        }
        List<BomDateZC> bomChild = bomDataMapper.selectBomChildInfo(parentBom.getFid());
//        log.info("查询bom{},获取的子物料：{}",productBomEntity.getProductBomCode(),bomChild.size());
//        if(productBomEntity.getProductBomCode().equals("17.0012.0071.99_V000")){
//            System.out.println("数据异常！");
//        }
        if (bomChild != null) {
            for (BomDateZC bomDateZC : bomChild) {

                // 每次循环的过程中都查找是否有符合条件的内容
                fullProductBomData(bomDateZC, productBomEntity);

                if (bomDateZC.getFid() != null && bomDateZC.getFid() > 0) {
                    List<BomDateZC> granChildBomList = execQueryBomChildData(bomDateZC, productBomEntity);
                    bomDateZC.setBomChild(granChildBomList);
                }
            }
            parentBom.setBomChild(bomChild);
        }
//       if(parentBom.getFid()==2128650){
//           System.out.println("target");
//       }
        return bomChild;
    }

    private void fullProductBomData(BomDateZC bom, ProductBomEntity productBomEntity) {
        String fnumber = bom.getFnumber();
        String fspecification = bom.getFspecification();
        /**
         *
         *    贴片半成品 13   13.0010 不需要体现在正查表中
         *    =============== ==========
         *    插件半成品 14
         *    =========================
         *    整机虚拟件 16.003
         *    =========================
         *    结构虚拟件 16.002
         *    =========================
         *    包装虚拟件 16.001
         *    =========================
         *
         *
         *    芯片 10.0240 开头
         *    =======================
         *    遥控器 10.065 开头
         *    =======================
         *    线材编码 编码为 12.0140、12.0180 12.0720
         *    =======================
         *    电池 10.045 开头
         *     =======================
         *    端口 10.0203
         *         * ============端口列===========
         *      * 插件接插件 hdmi 10.0380
         *      *========== 插件接插件 hdmi =============
         *      *
         *      * 贴片接插件 hdmi 10.0382
         *      * ========== 贴片接插件 hdmi底座 =========
         */
        if (fnumber.startsWith("13") && !fnumber.startsWith("13.0010") ) {
            if (StringUtils.isNotEmpty(productBomEntity.getSemiProductCode())) {
                fnumber = productBomEntity.getSemiProductCode() + "/" + fnumber;
                fspecification = productBomEntity.getSemiProductSpec() + "/" + fspecification;
            }
            productBomEntity.setSemiProductCode(fnumber);
            productBomEntity.setSemiProductSpec(fspecification);
        } else if ( fnumber.startsWith("14")) {
            if (StringUtils.isNotEmpty(productBomEntity.getInsertSemiCode())) {
                fnumber = productBomEntity.getInsertSemiCode() + "/" + fnumber;
                fspecification = productBomEntity.getInsertSemiSpec() + "/" + fspecification;
            }
            productBomEntity.setInsertSemiCode(fnumber);
            productBomEntity.setInsertSemiSpec(fspecification);
        } else if (fnumber.startsWith("16.003")) {
            if (StringUtils.isNotEmpty(productBomEntity.getVirtualWholeCode())) {
                fnumber = productBomEntity.getVirtualWholeCode() + "/" + fnumber;
                fspecification = productBomEntity.getVirtualWholeSpec() + "/" + fspecification;
            }
            productBomEntity.setVirtualWholeCode(fnumber);
            productBomEntity.setVirtualWholeSpec(fspecification);

        } else if (fnumber.startsWith("16.002")) {
            if (StringUtils.isNotEmpty(productBomEntity.getVirtualStructCode())) {
                fnumber = productBomEntity.getVirtualStructCode() + "/" + fnumber;
                fspecification = productBomEntity.getVirtualStructSpec() + "/" + fspecification;
            }
            productBomEntity.setVirtualStructCode(fnumber);
            productBomEntity.setVirtualStructSpec(fspecification);

        } else if (fnumber.startsWith("16.001")){
            if (StringUtils.isNotEmpty(productBomEntity.getVirtualPackageCode())) {
                fnumber = productBomEntity.getVirtualPackageCode() + "/" + fnumber;
                fspecification = productBomEntity.getVirtualPackageSpec() + "/" + fspecification;
            }
            productBomEntity.setVirtualPackageCode(fnumber);
            productBomEntity.setVirtualPackageSpec(fspecification);

        }else if (fnumber.startsWith("10.065")) {
            if (StringUtils.isNotEmpty(productBomEntity.getRemoteControllerCode())) {
                fnumber = productBomEntity.getRemoteControllerCode() + "/" + fnumber;
                fspecification = productBomEntity.getRemoteControllerDesc() + "/" + fspecification;
            }
            productBomEntity.setRemoteControllerCode(fnumber);
            productBomEntity.setRemoteControllerDesc(fspecification);
        } else if (fnumber.startsWith("12.0140") || fnumber.startsWith("12.0180") || fnumber.startsWith("12.0720")) {
            if (StringUtils.isNotEmpty(productBomEntity.getWireCode())) {
                fnumber = productBomEntity.getWireCode() + "/" + fnumber;
                fspecification = productBomEntity.getWireDesc() + "/" + fspecification;
            }
            productBomEntity.setWireCode(fnumber);
            productBomEntity.setWireDesc(fspecification);
        } else if (fnumber.startsWith("10.045")) {
            if (StringUtils.isNotEmpty(productBomEntity.getBatteryCode())) {
                fnumber = productBomEntity.getBatteryCode() + "/" + fnumber;
                fspecification = productBomEntity.getBatteryDesc() + "/" + fspecification;
            }
            productBomEntity.setBatteryCode(fnumber);
            productBomEntity.setBatteryDesc(fspecification);
        } else if (fnumber.startsWith("10.0203") && (StringUtils.isNotEmpty(fspecification) && fspecification.indexOf("HDMI") > -1)) {
            if (StringUtils.isNotEmpty(productBomEntity.getPortCode())) {
                fnumber = productBomEntity.getPortCode() + "/" + fnumber;
                fspecification = productBomEntity.getPortDesc() + "/" + fspecification;
            }
            productBomEntity.setPortCode(fnumber);
            productBomEntity.setPortDesc(fspecification);
        }else if (fnumber.startsWith("10.0240")) {
            if (StringUtils.isNotEmpty(productBomEntity.getChipCode())) {
                fnumber = productBomEntity.getChipCode() + "/" + fnumber;
                fspecification = productBomEntity.getChipSpec() + "/" + fspecification;
            }
            productBomEntity.setChipCode(fnumber);
            productBomEntity.setChipSpec(fspecification);
        }else if (fnumber.startsWith("10.0380")) {
            if (StringUtils.isNotEmpty(productBomEntity.getInsertHdmiCode())) {
                fnumber = productBomEntity.getInsertHdmiCode() + "/" + fnumber;
                fspecification = productBomEntity.getInsertHdmiDesc() + "/" + fspecification;
            }
            productBomEntity.setInsertHdmiCode(fnumber);
            productBomEntity.setInsertHdmiDesc(fspecification);
        } else if (fnumber.startsWith("10.0382")){
            if (StringUtils.isNotEmpty(productBomEntity.getPatchHdmiCode())) {
                fnumber = productBomEntity.getPatchHdmiCode() + "/" + fnumber;
                fspecification = productBomEntity.getPatchHdmiDesc() + "/" + fspecification;
            }
            productBomEntity.setPatchHdmiCode(fnumber);
            productBomEntity.setPatchHdmiDesc(fspecification);
        }
    }


    /**
     * 获取所有物料数据
     */
    @Override
    public Map<String, Integer> queryAllMaterialData() {
        List<BomDateZC> bomDataList = bomDataMapper.selectAllMaterial();
        Map<String, Integer> materialAndChangeMaterids = new HashMap<>();
        log.info("金蝶所有物料数据：{}", bomDataList.size());
        for (BomDateZC bomData : bomDataList) {
            materialAndChangeMaterids.put(bomData.getFnumber(), bomData.getFmaterialId());
        }
        return materialAndChangeMaterids;
    }

    /**
     * 遍历所有准备好的替换数据并开始操作
     */
    @Override
    public void startReplaceDataByReady(List<BomMaterialReplaceEntity> materialAndChangeMaterids) {
        // 操作后需要返回的信息要收集返回
        try {
            for (BomMaterialReplaceEntity bomMaterialReplaceEntity : materialAndChangeMaterids) {
                // string 转换成map
                Map<String, Integer> replaceMateridsIdsMap = objectMapper.readValue(bomMaterialReplaceEntity.getReplaceMateridsIds(), Map.class);
                // 获取所有 value 成为 set
                Set<Integer> replaceMaterialIdsSet = new HashSet<>(replaceMateridsIdsMap.values());
                List<BomChild> bomChildList = bomDataMapper.queryBomDataByFnumberAndChildMaterialId(bomMaterialReplaceEntity.getFbomNumber(), replaceMaterialIdsSet);
                if (replaceMaterialIdsSet.size() != bomChildList.size()) {
                    //不相等则数据不一致
                    bomMaterialReplaceEntity.setErrorMessage("物料数据对不上！");
                } else {
                    // 获取bomChildList entityId set
                    Set<Integer> entryIdsSet;
                    entryIdsSet = bomChildList.stream().map(BomChild::getFENTRYID).collect(Collectors.toSet());
                    Iterator<Integer> iterator = entryIdsSet.iterator();
                    // 获取一个元素并保留 更新
                    Integer updateEntityId = iterator.next();
                    if(replaceMaterialIdsSet.size()>1){
                        iterator.remove();
                        bomDataMapper.deleteBomChildByEntryId(entryIdsSet);
                    }
                    bomDataMapper.updateBomChildMaterialIdByEntryId(updateEntityId, bomMaterialReplaceEntity.getTargetMaterid());
                    bomMaterialReplaceEntity.setChanged(true);
                    bomMaterialReplaceEntity.setChangeMessage("成功替换！替换 ID：" + updateEntityId + "其他 Id删除：" + entryIdsSet);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    Pattern machineCasePattern = Pattern.compile( "([-][^-]*机壳[^-]*[-])|([_][^_]*机壳[^_]*[_])|" +
            "([^-]*机壳[^-]*[-])|([^_]*机壳[^_]*[_])|" +
            "([-][^-]*机壳[^-]*)|([_][^_]*机壳[^_]*)");
    Pattern hdmiPattern = Pattern.compile("HDMI");
    @Override
    public void queryAndChangeMaterialDescription() {

        List<MaterialLEntity> materialLEntities = bomDataMapper.queryMaterialDescription();
        materialLEntities.forEach(materialLEntity -> {

            boolean needChange = false;
            // 正则表达式匹配：-%机壳%- 或 %机壳%- 或 -%机壳%
            String result = materialLEntity.getFSPECIFICATION();

            // 先处理“机壳”相关模式
            Matcher machineCasematcher = machineCasePattern.matcher(result);
            while (machineCasematcher.find()) {
                needChange = true;
                machineCasematcher.reset(); // 重置 Matcher 以进行替换
                StringBuffer sb = new StringBuffer();
                while (machineCasematcher.find()) {
                    String match = machineCasematcher.group();
                    if ((match.startsWith("-") && match.endsWith("-")) ||
                            (match.startsWith("_") && match.endsWith("_"))) {
                        machineCasematcher.appendReplacement(sb, match.startsWith("-") ? "-" : "_"); // -%机壳%- 或 _%机壳%_ 替换为分隔符
                    } else {
                        machineCasematcher.appendReplacement(sb, ""); // %机壳%- 或 %机壳%_ 或 -%机壳% 或 _%机壳% 替换为空
                    }
                }
                machineCasematcher.appendTail(sb);
                result = sb.toString();
            }

            Matcher hdmiCasematcher = hdmiPattern.matcher(result);
            if (hdmiCasematcher.find()) {
                needChange = true;
                result = result.replaceAll("HDMI", "");
            }

            if(needChange){
                bomDataMapper.updateMaterialDescription(materialLEntity.getFPKID(), result);
                materialLEntity.setFSPECIFICATION( result);
            }
        });

    }


}
