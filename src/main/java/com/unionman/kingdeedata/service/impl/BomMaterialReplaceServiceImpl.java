package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.unionman.kingdeedata.entity.BomData;
import com.unionman.kingdeedata.entity.BomDateZC;
import com.unionman.kingdeedata.excel.entity.BomMaterialReplaceEntity;
import com.unionman.kingdeedata.mapper.temp.BomMaterialReplaceMapper;
import com.unionman.kingdeedata.service.BomDataService;
import com.unionman.kingdeedata.service.BomMaterialReplaceService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * BOM物料替换服务实现类
 */
@Service
@RequiredArgsConstructor
@DS("tempdb")
@Transactional
public class BomMaterialReplaceServiceImpl extends ServiceImpl<BomMaterialReplaceMapper, BomMaterialReplaceEntity> implements BomMaterialReplaceService {

    private final BomMaterialReplaceMapper bomMaterialReplaceMapper;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void queryBomMaterialReplaceDataAndChange(Map<String, Integer> materialAndChangeMaterids) {
        // 获取所有物料数据
        // 获取所有需要调整的数据
        try {
            List<BomMaterialReplaceEntity> bomMaterialReplaceEntities = bomMaterialReplaceMapper.selectList(null);
            for (BomMaterialReplaceEntity bomMaterialReplaceEntity : bomMaterialReplaceEntities) {

                if (materialAndChangeMaterids.containsKey(bomMaterialReplaceEntity.getTargetNumber())) {
                    bomMaterialReplaceEntity.setTargetMaterid(materialAndChangeMaterids.get(bomMaterialReplaceEntity.getTargetNumber()));
                } else {
                    bomMaterialReplaceEntity.setErrorMessage("未发目标物料 ID");
                    continue;
                }
                String replaceFnumberStr = bomMaterialReplaceEntity.getReplaceNumber();
                String[] replaceFunmberArray = new String[]{replaceFnumberStr};
                if (replaceFnumberStr.contains("/")) {
                    replaceFunmberArray = replaceFnumberStr.split("/");
                }
                Map<String, Integer> replaceMateridsIdsMap = new HashMap<>();
                for (String replaceFnumber : replaceFunmberArray) {
                    if (materialAndChangeMaterids.containsKey(replaceFnumber)) {

                        replaceMateridsIdsMap.put(replaceFnumber, materialAndChangeMaterids.get(replaceFnumber));
                    } else {
                        bomMaterialReplaceEntity.setErrorMessage("未发现需替换物料 ID");
                        continue;
                    }
                }
                String replaceMateridsIdsStr = objectMapper.writeValueAsString(replaceMateridsIdsMap);
                bomMaterialReplaceEntity.setReplaceMateridsIds(replaceMateridsIdsStr);

            }
            bomMaterialReplaceMapper.insertOrUpdate(bomMaterialReplaceEntities);

        } catch (Exception e) {
            // 处理异常
            throw new RuntimeException("查询BOM物料替换数据异常", e);
        }
    }

    /**
     * 查询所有BOM物料替换数据
     * @return
     */
    @Override
    public List<BomMaterialReplaceEntity> queryAllBomMaterialReplaceReadyData() {
        try {
            List<BomMaterialReplaceEntity> bomMaterialReplaceEntities = bomMaterialReplaceMapper.selectList(null);
            return bomMaterialReplaceEntities;
        } catch (Exception e) {
            // 处理异常
            throw new RuntimeException("查询BOM物料替换数据异常", e);
        }
    }

//    @Override
//    public void updateBatcEntitylist(List<BomMaterialReplaceEntity> entityList) {
//        bomMaterialReplaceMapper.insertOrUpdate(entityList);
//    }


}
