package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unionman.kingdeedata.excel.entity.ProductBomEntity;
import com.unionman.kingdeedata.mapper.temp.ProductBomMapper;
import com.unionman.kingdeedata.service.ProductBomService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@DS("tempdb")
@Transactional
public class ProductBomServiceImpl extends ServiceImpl<ProductBomMapper, ProductBomEntity> implements ProductBomService {
    private  final ProductBomMapper productBomMapper;


    @Override
    public void reSaveData(List<ProductBomEntity> newDataRow) {
        productBomMapper.delete(null);
        saveBatch(newDataRow);
    }
}
