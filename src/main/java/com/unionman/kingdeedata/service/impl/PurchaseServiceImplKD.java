package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import com.unionman.kingdeedata.entitytmp.ProcurementAdjustmentRecordEntity;
import com.unionman.kingdeedata.mapper.kingdee.ProcurementOrderMapper;
import com.unionman.kingdeedata.service.PurchaseServiceKD;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@DS("kingdeedb")
@RequiredArgsConstructor
public class PurchaseServiceImplKD implements PurchaseServiceKD {

    private final ProcurementOrderMapper procurementOrderMapper;

    @Override
    public void queryPurchaseEntryID(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity) {

        // 查询采购申请单
        List<BaseMainEntryIdEntity> requisitionData = procurementOrderMapper.queryPurchaseRequisition(procurementAdjustmentRecordEntity.getPurchaseApplicationNo(), procurementAdjustmentRecordEntity.getMaterialCodeToAdjustId());
        procurementAdjustmentRecordEntity.setPurchaseApplicationEntryIds(requisitionData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
        // 查询采购订单
        List<BaseMainEntryIdEntity> orderData = procurementOrderMapper.queryPurchaseOrder(procurementAdjustmentRecordEntity.getPurchaseOrderNo(), procurementAdjustmentRecordEntity.getMaterialCodeToAdjustId());
        procurementAdjustmentRecordEntity.setPurchaseOrderEntryIds(orderData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
        // 查询采购收料单
        List<BaseMainEntryIdEntity> receiveData = procurementOrderMapper.queryPurchaseReceive(procurementAdjustmentRecordEntity.getPurchaseReceiptNo(), procurementAdjustmentRecordEntity.getMaterialCodeToAdjustId());
        procurementAdjustmentRecordEntity.setPurchaseReceiptEntryIds(receiveData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
        // 查询采购入库单
        List<BaseMainEntryIdEntity> inStockData = procurementOrderMapper.queryPurchaseInStock(procurementAdjustmentRecordEntity.getPurchaseWarehouseInNo(), procurementAdjustmentRecordEntity.getMaterialCodeToAdjustId());
        procurementAdjustmentRecordEntity.setPurchaseWarehouseInEntryIds(inStockData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
        // 查询退料单
        List<BaseMainEntryIdEntity> backStockData = procurementOrderMapper.queryPurchaseBackStock(procurementAdjustmentRecordEntity.getPurchaseBackNo(), procurementAdjustmentRecordEntity.getMaterialCodeToAdjustId());
        procurementAdjustmentRecordEntity.setPurchaseBackEntryIds(backStockData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
    }



    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updatePurchaseRequisitionEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity) {
        if(!procurementAdjustmentRecordEntity.getPurchaseApplicationEntryIds().isEmpty()){
            procurementOrderMapper.updatePurchaseRequisitionEntry(procurementAdjustmentRecordEntity.getTargetMaterialCodeId(), procurementAdjustmentRecordEntity.getPurchaseApplicationEntryIds());
            procurementAdjustmentRecordEntity.setPurchaseApplicationAdjustResult(procurementAdjustmentRecordEntity.getPurchaseApplicationEntryIds().toString() + "成功");
        }else{
            procurementAdjustmentRecordEntity.setPurchaseApplicationAdjustResult("没有需要调整的明细行");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updatePurchaseOrderEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity) {
        if(!procurementAdjustmentRecordEntity.getPurchaseOrderEntryIds().isEmpty()){
            procurementOrderMapper.updatePurchaseOrderEntry(procurementAdjustmentRecordEntity.getTargetMaterialCodeId(), procurementAdjustmentRecordEntity.getPurchaseOrderEntryIds());
            procurementAdjustmentRecordEntity.setPurchaseOrderAdjustResult(procurementAdjustmentRecordEntity.getPurchaseOrderEntryIds().toString() + "成功");
        }else {
            procurementAdjustmentRecordEntity.setPurchaseOrderAdjustResult("没有需要调整的明细行");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updatePurchaseReceiveEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity) {
        if(!procurementAdjustmentRecordEntity.getPurchaseReceiptEntryIds().isEmpty()){
            procurementOrderMapper.updatePurchaseReceiveEntry(procurementAdjustmentRecordEntity.getTargetMaterialCodeId(), procurementAdjustmentRecordEntity.getPurchaseReceiptEntryIds());
            procurementAdjustmentRecordEntity.setPurchaseReceiptAdjustResult(procurementAdjustmentRecordEntity.getPurchaseReceiptEntryIds().toString() + "成功");
        }else {
            procurementAdjustmentRecordEntity.setPurchaseReceiptAdjustResult("没有需要调整的明细行");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updatePurchaseInStockEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity) {
        if(!procurementAdjustmentRecordEntity.getPurchaseWarehouseInEntryIds().isEmpty()){
            procurementOrderMapper.updatePurchaseInStockEntry(procurementAdjustmentRecordEntity.getTargetMaterialCodeId(), procurementAdjustmentRecordEntity.getPurchaseWarehouseInEntryIds());
            procurementAdjustmentRecordEntity.setPurchaseWarehouseInAdjustResult(procurementAdjustmentRecordEntity.getPurchaseWarehouseInEntryIds().toString() + "成功");
        }else {
            procurementAdjustmentRecordEntity.setPurchaseWarehouseInAdjustResult("无没有需要调整的明细行");
        }
    }



    //采购退料单

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void queryPurchaseBackStockEntry(ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity) {
        if(!procurementAdjustmentRecordEntity.getPurchaseBackEntryIds().isEmpty()){
            List<BaseMainEntryIdEntity> backStockData = procurementOrderMapper.queryPurchaseBackStock(procurementAdjustmentRecordEntity.getPurchaseBackNo(), procurementAdjustmentRecordEntity.getMaterialCodeToAdjustId());
            procurementAdjustmentRecordEntity.setPurchaseBackEntryIds(backStockData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
        }else{
            procurementAdjustmentRecordEntity.setPurchaseBackAdjustResult("没有需要调整的明细行");
        }

    }
}
