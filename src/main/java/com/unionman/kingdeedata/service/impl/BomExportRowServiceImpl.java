package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unionman.kingdeedata.excel.entity.BomExportRow;
import com.unionman.kingdeedata.mapper.temp.BomExportRowMapper;
import com.unionman.kingdeedata.service.BomExportRowService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@DS("tempdb")
@Transactional
public class BomExportRowServiceImpl extends ServiceImpl<BomExportRowMapper, BomExportRow> implements BomExportRowService {

    private final BomExportRowMapper bomExportRowMapper;

    @Override
    public void reSaveData( List<BomExportRow> rows ) {
        // 能删除所有？
        bomExportRowMapper.delete(null);
       saveBatch(rows);
    }

    @Override
    public List<BomExportRow> getAll() {
        return  bomExportRowMapper.selectDistinctProductData();
    }
}
