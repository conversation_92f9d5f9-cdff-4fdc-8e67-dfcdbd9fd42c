package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unionman.kingdeedata.entitytmp.ProcurementAdjustmentRecordEntity;
import com.unionman.kingdeedata.mapper.temp.ProcurementAdjustmentRecordMapper;
import com.unionman.kingdeedata.service.ProcurementAdjustmentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Transactional
@DS("tempdb")
public class ProcurementAdjustmentServiceImpl extends ServiceImpl<ProcurementAdjustmentRecordMapper, ProcurementAdjustmentRecordEntity> implements ProcurementAdjustmentService  {
}
