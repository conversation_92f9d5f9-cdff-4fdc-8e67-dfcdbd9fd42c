package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import com.unionman.kingdeedata.entitytmp.InitialInventoryAdjustmentEntity;
import com.unionman.kingdeedata.mapper.kingdee.InitialInventoryMapper;
import com.unionman.kingdeedata.service.InitialInventoryServiceKD;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@DS("kingdeedb")
@RequiredArgsConstructor
public class InitialInventoryServiceImplKD implements InitialInventoryServiceKD {
    private final InitialInventoryMapper initialInventoryMapper;

    @Override
    public void queryInitialInventoryEntryID(InitialInventoryAdjustmentEntity initialInventoryAdjustmentEntity) {
        List<BaseMainEntryIdEntity> initialInventoryData =   initialInventoryMapper.queryInitialInventoryMaterial(initialInventoryAdjustmentEntity.getInitialInventoryNo(), initialInventoryAdjustmentEntity.getMaterialCodeToAdjustId());
        initialInventoryAdjustmentEntity.setInitialInventoryEntryIds(initialInventoryData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());

    }

    @Override
    public void updateInitialInventoryByEntryID(InitialInventoryAdjustmentEntity initialInventoryAdjustmentEntity) {
        if (!initialInventoryAdjustmentEntity.getInitialInventoryEntryIds().isEmpty()){
            initialInventoryMapper.updateInitialInventoryEntry(initialInventoryAdjustmentEntity.getMaterialCodeToAdjustId(), initialInventoryAdjustmentEntity.getInitialInventoryEntryIds());
            initialInventoryAdjustmentEntity.setInitialInventoryAdjustResult(initialInventoryAdjustmentEntity.getInitialInventoryEntryIds().toString() + "成功");
        }else {
            initialInventoryAdjustmentEntity.setInitialInventoryAdjustResult("没有需要调整的明细行");
        }
    }
}
