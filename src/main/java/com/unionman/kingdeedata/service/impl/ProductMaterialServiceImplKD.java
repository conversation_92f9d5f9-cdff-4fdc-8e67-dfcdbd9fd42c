package com.unionman.kingdeedata.service.impl;

import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import com.unionman.kingdeedata.entitytmp.ProductMaterialAdjustmentEntity;
import com.unionman.kingdeedata.mapper.kingdee.ProductMaterialMapper;
import com.unionman.kingdeedata.service.ProductMaterialServiceKD;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ProductMaterialServiceImplKD implements ProductMaterialServiceKD {
    private final ProductMaterialMapper productMaterialMapper;

    @Override
    public void queryProductMaterialEntryID(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity) {
     List<BaseMainEntryIdEntity> productMaterialData =   productMaterialMapper.queryProductMaterial(productMaterialAdjustmentEntity.getProductionOrderNo(), productMaterialAdjustmentEntity.getMaterialCodeToAdjustId());
        productMaterialAdjustmentEntity.setProductionOrderEntryIds(productMaterialData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
    }

    @Override
    public void updateProductMaterialEntry(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity) {
        if(!productMaterialAdjustmentEntity.getProductionOrderEntryIds().isEmpty()){
            productMaterialMapper.updateProductMaterialEntry(productMaterialAdjustmentEntity.getTargetMaterialCodeId(), productMaterialAdjustmentEntity.getProductionOrderEntryIds());
            productMaterialAdjustmentEntity.setProductionOrderAdjustResult(productMaterialAdjustmentEntity.getProductionOrderEntryIds().toString() + "成功");
        }else {
            productMaterialAdjustmentEntity.setProductionOrderAdjustResult("没有需要调整的明细行");
        }
    }

    @Override
    public void queryProductPickingMaterialEntryID(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity) {
        List<BaseMainEntryIdEntity> productPickingMaterialData =   productMaterialMapper.queryProductPickingMaterial(productMaterialAdjustmentEntity.getProductionPickingNo(), productMaterialAdjustmentEntity.getMaterialCodeToAdjustId());
        productMaterialAdjustmentEntity.setProductionPickingEntryIds(productPickingMaterialData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
    }

    @Override
    public void updateProductPickingMaterialEntry(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity) {
        if(!productMaterialAdjustmentEntity.getProductionPickingEntryIds().isEmpty()){
            productMaterialMapper.updateProductPickingMaterialEntry(productMaterialAdjustmentEntity.getTargetMaterialCodeId(), productMaterialAdjustmentEntity.getProductionPickingEntryIds());
            productMaterialAdjustmentEntity.setProductionPickingAdjustResult(productMaterialAdjustmentEntity.getProductionPickingEntryIds().toString() + "成功");
        }else {
            productMaterialAdjustmentEntity.setProductionPickingAdjustResult("没有需要调整的明细行");
        }
    }

    @Override
    public void queryProductBackMaterialEntryID(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity) {

        List<BaseMainEntryIdEntity> productBackMaterialData =   productMaterialMapper.queryProductBackMaterial(productMaterialAdjustmentEntity.getProductionBackNo(), productMaterialAdjustmentEntity.getMaterialCodeToAdjustId());
        productMaterialAdjustmentEntity.setProductionBackEntryIds(productBackMaterialData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
    }

    @Override
    public void updateProductBackMaterialEntry(ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity) {
        if ( !productMaterialAdjustmentEntity.getProductionBackEntryIds().isEmpty()){
         productMaterialMapper.updateProductBackMaterialEntry(productMaterialAdjustmentEntity.getTargetMaterialCodeId(), productMaterialAdjustmentEntity.getProductionBackEntryIds());
         productMaterialAdjustmentEntity.setProductionBackAdjustResult(productMaterialAdjustmentEntity.getProductionBackEntryIds().toString() + "成功");
        }else{
            productMaterialAdjustmentEntity.setProductionBackAdjustResult("没有需要调整的明细行");
        }
    }
}
