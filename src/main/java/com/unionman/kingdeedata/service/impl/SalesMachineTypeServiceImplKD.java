package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import com.unionman.kingdeedata.entitytmp.SalesMachineTypeAdjustmentEntity;
import com.unionman.kingdeedata.entitytmp.SalesMaterialAdjustmentEntity;
import com.unionman.kingdeedata.mapper.kingdee.SalesOrderMapper;
import com.unionman.kingdeedata.service.SalesMachineTypeServiceKD;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@DS("kingdeedb")
@RequiredArgsConstructor
public class SalesMachineTypeServiceImplKD implements SalesMachineTypeServiceKD {
    private final SalesOrderMapper salesOrderMapper;


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateSalesOrderMachineType(SalesMachineTypeAdjustmentEntity entity) {
        if (entity.getSalesOrderCode() != null && !entity.getSalesOrderCode().isEmpty()) {
            List<BaseMainEntryIdEntity> salesOrders = salesOrderMapper.querySalesOrder(entity.getSalesOrderCode());
            if (!salesOrders.isEmpty()) {
                BaseMainEntryIdEntity salesOrder = salesOrders.get(0);
                salesOrderMapper.updateSalesOrderMachineType(salesOrder.getFid(), entity.getTargetMachineType());
                entity.setSalesOrderAdjustResult("成功:"+salesOrder.getFid().toString());
            } else {
                entity.setSalesOrderAdjustResult("未找到销售订单");
            }
        } else {
            entity.setSalesOrderAdjustResult("销售订单编码为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateDeliveryNoticeMachineType(SalesMachineTypeAdjustmentEntity entity) {
        if (entity.getDeliveryNoticeCode() != null && !entity.getDeliveryNoticeCode().isEmpty()) {
            List<BaseMainEntryIdEntity> deliveryNotices = salesOrderMapper.queryDeliveryNotice(entity.getDeliveryNoticeCode());
            if (!deliveryNotices.isEmpty()) {
                BaseMainEntryIdEntity deliveryNotice = deliveryNotices.get(0);
                salesOrderMapper.updateDeliveryNoticeMachineType(deliveryNotice.getFid(), entity.getTargetMachineType());
                entity.setDeliveryNoticeAdjustResult("成功:"+deliveryNotice.getFid().toString());
            } else {
                entity.setDeliveryNoticeAdjustResult("未找到发货通知单");
            }
        } else {
            entity.setDeliveryNoticeAdjustResult("发货通知单编码为空");
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateSalesOutboundMachineType(SalesMaterialAdjustmentEntity entity) {
        if (entity.getSalesOutboundCode() != null && !entity.getSalesOutboundCode().isEmpty()) {
            List<BaseMainEntryIdEntity> salesOutbounds = salesOrderMapper.querySalesOutstockEntryId(entity.getSalesOutboundCode(), entity.getMaterialCodeToAdjustId());
            entity.setSalesOutboundEntryIds(salesOutbounds.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
        } else {
            entity.setSalesOutboundAdjustResult("销售出库单编码为空");
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateSalesOutboundEntry(SalesMaterialAdjustmentEntity entity) {
        if(entity.getSalesOutboundEntryIds().isEmpty()){
            entity.setSalesOutboundAdjustResult("未找到销售对应物料的销售出库单");
        }else{
            salesOrderMapper.updateSalesOutstockEntry(entity.getTargetMaterialCodeId(), entity.getSalesOutboundEntryIds());
            entity.setSalesOutboundAdjustResult("成功:"+entity.getSalesOutboundEntryIds().toString());
        }

    }

    /**
     * 查询发货通知单明细
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateDeliveryNoticeEntry(SalesMaterialAdjustmentEntity entity) {
        if (entity.getDeliveryNoticeCode() != null && !entity.getDeliveryNoticeCode().isEmpty()) {
            List<BaseMainEntryIdEntity> deliveryNotices = salesOrderMapper.queryDeliveryNoticeEntryIdByMaterialId(entity.getDeliveryNoticeCode(), entity.getMaterialCodeToAdjustId());
            entity.setDeliveryNoticeEntryIds(deliveryNotices.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
        } else {
            entity.setDeliveryNoticeAdjustResult("发货通知单编码为空");
        }
    }

    /**
     * 跟新发货通知单物料
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateDeliveryNoticeMaterial(SalesMaterialAdjustmentEntity entity) {
        if(entity.getDeliveryNoticeEntryIds().isEmpty()){
            entity.setDeliveryNoticeAdjustResult("未找到应物料的发货通知单");
        }else{
            salesOrderMapper.updateDeliveryNoticeMaterial(entity.getTargetMaterialCodeId(), entity.getDeliveryNoticeEntryIds());
            entity.setDeliveryNoticeAdjustResult("成功:"+entity.getDeliveryNoticeEntryIds().toString());
        }
    }

}
