package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import com.unionman.kingdeedata.entitytmp.SalesMachineTypeAdjustmentEntity;
import com.unionman.kingdeedata.mapper.kingdee.SalesOrderMapper;
import com.unionman.kingdeedata.service.SalesMachineTypeServiceKD;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@DS("kingdeedb")
@RequiredArgsConstructor
public class SalesMachineTypeServiceImplKD implements SalesMachineTypeServiceKD {
    private final SalesOrderMapper salesOrderMapper;


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateSalesOrderMachineType(SalesMachineTypeAdjustmentEntity entity) {
        if (entity.getSalesOrderCode() != null && !entity.getSalesOrderCode().isEmpty()) {
            List<BaseMainEntryIdEntity> salesOrders = salesOrderMapper.querySalesOrder(entity.getSalesOrderCode());
            if (!salesOrders.isEmpty()) {
                BaseMainEntryIdEntity salesOrder = salesOrders.get(0);
                salesOrderMapper.updateSalesOrderMachineType(salesOrder.getFid(), entity.getTargetMachineType());
                entity.setSalesOrderAdjustResult("成功:"+salesOrder.getFid().toString());
            } else {
                entity.setSalesOrderAdjustResult("未找到销售订单");
            }
        } else {
            entity.setSalesOrderAdjustResult("销售订单编码为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateDeliveryNoticeMachineType(SalesMachineTypeAdjustmentEntity entity) {
        if (entity.getDeliveryNoticeCode() != null && !entity.getDeliveryNoticeCode().isEmpty()) {
            List<BaseMainEntryIdEntity> deliveryNotices = salesOrderMapper.queryDeliveryNotice(entity.getDeliveryNoticeCode());
            if (!deliveryNotices.isEmpty()) {
                BaseMainEntryIdEntity deliveryNotice = deliveryNotices.get(0);
                salesOrderMapper.updateDeliveryNoticeMachineType(deliveryNotice.getFid(), entity.getTargetMachineType());
                entity.setDeliveryNoticeAdjustResult("成功:"+deliveryNotice.getFid().toString());
            } else {
                entity.setDeliveryNoticeAdjustResult("未找到发货通知单");
            }
        } else {
            entity.setDeliveryNoticeAdjustResult("发货通知单编码为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateSalesOutboundMachineType(SalesMachineTypeAdjustmentEntity entity) {
        if (entity.getSalesOutboundCode() != null && !entity.getSalesOutboundCode().isEmpty()) {
            List<BaseMainEntryIdEntity> salesOutbounds = salesOrderMapper.querySalesOutstock(entity.getSalesOutboundCode());
            if (!salesOutbounds.isEmpty()) {
                BaseMainEntryIdEntity salesOutbound = salesOutbounds.get(0);
                salesOrderMapper.updateSalesOutstockMachineType(salesOutbound.getFid(), entity.getTargetMachineType());
                entity.setSalesOutboundAdjustResult("成功:"+salesOutbound.getFid());
            } else {
                entity.setSalesOutboundAdjustResult("未找到销售出库单");
            }
        } else {
            entity.setSalesOutboundAdjustResult("销售出库单编码为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateSalesOutboundEntry(SalesMachineTypeAdjustmentEntity entity) {
        if (entity.getSalesOutboundCode() != null && !entity.getSalesOutboundCode().isEmpty()) {
}
