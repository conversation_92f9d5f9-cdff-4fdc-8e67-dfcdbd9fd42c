package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import com.unionman.kingdeedata.entitytmp.OutsourcingMaterialAdjustmentEntity;
import com.unionman.kingdeedata.mapper.kingdee.OutsourcingMapper;
import com.unionman.kingdeedata.service.OutsourcingService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 委外相关业务实现类
 * <AUTHOR>
 */
@Service
@DS("kingdeedb")
@RequiredArgsConstructor
public class OutsourcingServiceImpl implements OutsourcingService {
    
    private final OutsourcingMapper outsourcingMapper;
    
    @Override
    public void queryOutsourcingMaterialAndPick(OutsourcingMaterialAdjustmentEntity outsourcingMaterialAdjustmentEntity) {
        // 查询委外用料
        List<BaseMainEntryIdEntity> outStockApplyData = outsourcingMapper.queryOutsourcingMaterial(outsourcingMaterialAdjustmentEntity.getOutsourcingMaterialNo(), outsourcingMaterialAdjustmentEntity.getSourceMaterialId());
        outsourcingMaterialAdjustmentEntity.setOutsourcingMaterialEntryIds(outStockApplyData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());


        // 查询委外领料
        List<BaseMainEntryIdEntity> outStockApplyDataPick = outsourcingMapper.queryOutsourcingPack(outsourcingMaterialAdjustmentEntity.getOutsourcingPickNo(), outsourcingMaterialAdjustmentEntity.getSourceMaterialId());
        outsourcingMaterialAdjustmentEntity.setOutsourcingPickEntryIds(outStockApplyDataPick.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
    }
    
    @Override
    public void updateOutsourcingEntry(OutsourcingMaterialAdjustmentEntity outsourcingMaterialAdjustmentEntity) {
        if(StringUtils.isNotEmpty(outsourcingMaterialAdjustmentEntity.getOutsourcingMaterialNo()) && !outsourcingMaterialAdjustmentEntity.getOutsourcingMaterialEntryIds().isEmpty()){
         outsourcingMapper.updateOutsourcingEntry(outsourcingMaterialAdjustmentEntity.getTargetMaterialId(), outsourcingMaterialAdjustmentEntity.getOutsourcingMaterialEntryIds());
            outsourcingMaterialAdjustmentEntity.setOutsourcingMaterialAdjustResult(outsourcingMaterialAdjustmentEntity.getOutsourcingMaterialEntryIds().toString()+"成功");
        }else {
            outsourcingMaterialAdjustmentEntity.setOutsourcingMaterialAdjustResult("无没有需要调整的明细行");
        }
    }
    
    @Override
    public void updateOutsourcingPickEntry(OutsourcingMaterialAdjustmentEntity outsourcingMaterialAdjustmentEntity) {

        if (StringUtils.isNotEmpty(outsourcingMaterialAdjustmentEntity.getOutsourcingPickNo()) && !outsourcingMaterialAdjustmentEntity.getOutsourcingPickEntryIds().isEmpty()){
            outsourcingMapper.updateOutsourcingPickEntry(outsourcingMaterialAdjustmentEntity.getTargetMaterialId(), outsourcingMaterialAdjustmentEntity.getOutsourcingPickEntryIds());
            outsourcingMaterialAdjustmentEntity.setOutsourcingPickAdjustResult(outsourcingMaterialAdjustmentEntity.getOutsourcingPickEntryIds().toString()+"成功");
        }else {
            outsourcingMaterialAdjustmentEntity.setOutsourcingPickAdjustResult("无没有需要调整的明细行");
        }
    }
}
