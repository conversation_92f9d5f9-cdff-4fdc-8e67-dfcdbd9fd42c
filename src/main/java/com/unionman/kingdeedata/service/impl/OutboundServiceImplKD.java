package com.unionman.kingdeedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import com.unionman.kingdeedata.entitytmp.OutboundAdjustmentRecordEntity;
import com.unionman.kingdeedata.mapper.kingdee.OutbundMapper;
import com.unionman.kingdeedata.service.OutboundServiceKD;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@DS("kingdeedb")
@RequiredArgsConstructor
public class OutboundServiceImplKD implements OutboundServiceKD {

    private final OutbundMapper outbundMapper;

    @Override
    public void queryOutboundEntryID(OutboundAdjustmentRecordEntity outboundAdjustmentRecordEntity) {
        // 查询出库申请单
        List<BaseMainEntryIdEntity> outStockApplyData = outbundMapper.queryOutStockApply(outboundAdjustmentRecordEntity.getApplicationNo(), outboundAdjustmentRecordEntity.getMaterialCodeToAdjustId());
        outboundAdjustmentRecordEntity.setApplicationNoEntryIds(outStockApplyData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());
        // 查询其他出库单
        List<BaseMainEntryIdEntity> otherOutStockData = outbundMapper.queryOtherOutStock(outboundAdjustmentRecordEntity.getOtherOutboundNo(), outboundAdjustmentRecordEntity.getMaterialCodeToAdjustId());
        outboundAdjustmentRecordEntity.setOtherOutboundEntryIds(otherOutStockData.stream().map(BaseMainEntryIdEntity::getFentryid).toList());

    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateOutStockApplyEntry(OutboundAdjustmentRecordEntity outboundAdjustmentRecordEntity) {
        if(StringUtils.isNotEmpty(outboundAdjustmentRecordEntity.getApplicationNo()) &&!outboundAdjustmentRecordEntity.getApplicationNoEntryIds().isEmpty()){
            outbundMapper.updateOutStockApplyEntry(outboundAdjustmentRecordEntity.getTargetMaterialCodeId(), outboundAdjustmentRecordEntity.getApplicationNoEntryIds());
            outboundAdjustmentRecordEntity.setApplicationAdjustResult(outboundAdjustmentRecordEntity.getApplicationNoEntryIds().toString() + "成功");
        }else {
            outboundAdjustmentRecordEntity.setApplicationAdjustResult("没有需要调整的明细行");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateOutStockEntry(OutboundAdjustmentRecordEntity outboundAdjustmentRecordEntity) {
        if(StringUtils.isNotEmpty(outboundAdjustmentRecordEntity.getOtherOutboundNo()) &&!outboundAdjustmentRecordEntity.getOtherOutboundEntryIds().isEmpty()){
            outbundMapper.updateOtherOutStockEntry(outboundAdjustmentRecordEntity.getTargetMaterialCodeId(), outboundAdjustmentRecordEntity.getOtherOutboundEntryIds());
            outboundAdjustmentRecordEntity.setOtherOutboundAdjustResult(outboundAdjustmentRecordEntity.getOtherOutboundEntryIds().toString() + "成功");
        }else {
            outboundAdjustmentRecordEntity.setOtherOutboundAdjustResult("没有需要调整的明细行");
        }
    }
}
