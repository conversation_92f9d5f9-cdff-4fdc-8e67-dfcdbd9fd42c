package com.unionman.kingdeedata.service;

import com.unionman.kingdeedata.entitytmp.SalesMachineTypeAdjustmentEntity;
import com.unionman.kingdeedata.entitytmp.SalesMaterialAdjustmentEntity;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

public interface SalesMachineTypeServiceKD {

    /**
     * 更新销售订单机器类型
     * @param entity 销售机器类型调整实体
     */
    void updateSalesOrderMachineType(SalesMachineTypeAdjustmentEntity entity);

    /**
     * 更新发货通知单机器类型
     * @param entity 销售机器类型调整实体
     */
    void updateDeliveryNoticeMachineType(SalesMachineTypeAdjustmentEntity entity);

    /**
     * 更新销售出库单机器类型
     * @param entity 销售机器类型调整实体
     */
    void updateSalesOutboundMachineType(SalesMachineTypeAdjustmentEntity entity);

    /**
     * 根据需要调整物料编码查询销售出库单明细
     * @param entity
     */
    void updateSalesOutboundMachineType(SalesMaterialAdjustmentEntity entity);

    /**
     * 调整对应销售出库单物料明细
     * @param entity
     */
    void updateSalesOutboundEntry(SalesMaterialAdjustmentEntity entity);

    /**
     * 根据需要调整物料编码查询发货通知单明细
     * @param entity
     */
    void updateDeliveryNoticeEntry(SalesMaterialAdjustmentEntity entity);

    /**
     * 批量更新发货通知单物料明细
     * @param entity
     */
    void updateDeliveryNoticeMaterial(SalesMaterialAdjustmentEntity entity);
}
