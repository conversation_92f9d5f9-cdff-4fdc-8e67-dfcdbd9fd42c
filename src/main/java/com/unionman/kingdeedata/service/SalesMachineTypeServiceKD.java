package com.unionman.kingdeedata.service;

import com.unionman.kingdeedata.entitytmp.SalesMachineTypeAdjustmentEntity;

public interface SalesMachineTypeServiceKD {

    /**
     * 更新销售订单机器类型
     * @param entity 销售机器类型调整实体
     */
    void updateSalesOrderMachineType(SalesMachineTypeAdjustmentEntity entity);

    /**
     * 更新发货通知单机器类型
     * @param entity 销售机器类型调整实体
     */
    void updateDeliveryNoticeMachineType(SalesMachineTypeAdjustmentEntity entity);

    /**
     * 更新销售出库单机器类型
     * @param entity 销售机器类型调整实体
     */
    void updateSalesOutboundMachineType(SalesMachineTypeAdjustmentEntity entity);
}
