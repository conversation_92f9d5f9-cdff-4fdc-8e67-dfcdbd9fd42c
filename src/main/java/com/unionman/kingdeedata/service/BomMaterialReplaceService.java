package com.unionman.kingdeedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.unionman.kingdeedata.excel.entity.BomMaterialReplaceEntity;

import java.util.List;
import java.util.Map;

/**
 * BOM物料替换服务接口
 */
public interface BomMaterialReplaceService extends IService<BomMaterialReplaceEntity> {

    void queryBomMaterialReplaceDataAndChange(Map<String,Integer> materialAndChangeMaterids);

    List<BomMaterialReplaceEntity> queryAllBomMaterialReplaceReadyData();

    //void updateBatcEntitylist(List<BomMaterialReplaceEntity> entityList);

}
