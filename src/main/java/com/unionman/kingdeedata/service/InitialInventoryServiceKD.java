package com.unionman.kingdeedata.service;

import com.unionman.kingdeedata.entitytmp.InitialInventoryAdjustmentEntity;

public interface InitialInventoryServiceKD {
    //初始库存查询
    void queryInitialInventoryEntryID(InitialInventoryAdjustmentEntity initialInventoryAdjustmentEntity);
    //初始库存更新
    void updateInitialInventoryByEntryID(InitialInventoryAdjustmentEntity initialInventoryAdjustmentEntity);
}
