package com.unionman.kingdeedata.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 /**
 * <AUTHOR>
 * 杜比数据调整项
 */
@Tag(name = "杜比调整", description = "bom数据反查、正差、调整")
@Slf4j
@RestController("/bomdata")
@RequiredArgsConstructor
public class duBeController {

    // 其他出库--跟 hdmi 的一样
    // 直接使用其他出库单


    // 采购入库- 已调整hdmi 增加退库操作

    // 销售出库// 需要单独制作


    // 用料&领料&退料


}
