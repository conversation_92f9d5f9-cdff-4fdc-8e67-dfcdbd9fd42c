package com.unionman.kingdeedata.controller;

import com.unionman.kingdeedata.MaterialCacheUtil;
import com.unionman.kingdeedata.entitytmp.OutboundAdjustmentRecordEntity;
import com.unionman.kingdeedata.entitytmp.ProcurementAdjustmentRecordEntity;
import com.unionman.kingdeedata.service.OutboundServiceKD;
import com.unionman.kingdeedata.service.ProcurementAdjustmentService;
import com.unionman.kingdeedata.service.PurchaseServiceKD;
import com.unionman.kingdeedata.servicetmp.OutboundAdjustmentRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "HDMI-其他出库数据调整", description = "其他出库数据调整-刘如")
@Slf4j
@RestController("/otherOutStackController")
@RequiredArgsConstructor
public class OutStackController {
    private final OutboundServiceKD outboundServiceKD;
    private final OutboundAdjustmentRecordService outboundAdjustmentRecordService;
    private final MaterialCacheUtil materialCacheUtil;


    /**
     * 采购数据调整
     */
    @GetMapping("/startOutStackAdjustment")
    @Operation(summary = "其他出库调整", description = "出库申请-其他出库单数据调整")
    public void startOutStackAdjustment() {
        // 获取所有需要调整的数据
        log.info("开始采购数据调整");
        List<OutboundAdjustmentRecordEntity> outboundAdjustmentRecordEntityList = outboundAdjustmentRecordService.list();
        log.info("开始遍历数据,共{}条数据", outboundAdjustmentRecordEntityList.size());
        // 获取所有数据金蝶 id
        for (OutboundAdjustmentRecordEntity outboundAdjustmentRecordEntity : outboundAdjustmentRecordEntityList) {
            // 根据物料缓存获取物料id
            Integer materialCodeToAdjustId = materialCacheUtil.getMaterialCache().get(outboundAdjustmentRecordEntity.getMaterialCodeToAdjust());
            Integer targetMaterialCodeId = materialCacheUtil.getMaterialCache().get(outboundAdjustmentRecordEntity.getTargetMaterialCode());
            if(materialCodeToAdjustId>0 && targetMaterialCodeId>0){
                // 设置物料id
                outboundAdjustmentRecordEntity.setMaterialCodeToAdjustId(Long.valueOf(materialCodeToAdjustId));
                outboundAdjustmentRecordEntity.setTargetMaterialCodeId(Long.valueOf(targetMaterialCodeId));
                // 根据订单编号+需替换的物料 Id 查询订单详情 id
                outboundServiceKD.queryOutboundEntryID(outboundAdjustmentRecordEntity);

                int count = 0;
                //查询完成进行更新
                try {
                    outboundServiceKD.updateOutStockApplyEntry(outboundAdjustmentRecordEntity);
                    count++;
                } catch (Exception e) {
                    log.error("出库申请单数据失败：{}", e.getMessage());
                }
                try {
                    outboundServiceKD.updateOutStockEntry(outboundAdjustmentRecordEntity);
                    count++;
                } catch (Exception e) {
                    log.error("其他出库单数据失败：{}", e.getMessage());
                }

                if(count>=2){
                    outboundAdjustmentRecordEntity.setFinalResult("[成功]");
                }else{
                    outboundAdjustmentRecordEntity.setFinalResult("[部分成功]：错误信息见不同调整项");
                }

            }else{
                outboundAdjustmentRecordEntity.setFinalResult("[错误]:物料编码不存在"+outboundAdjustmentRecordEntity.getMaterialCodeToAdjust()+"->"+materialCodeToAdjustId+";"+outboundAdjustmentRecordEntity.getTargetMaterialCode()+"->"+targetMaterialCodeId);
            }
        }
        log.info("其他出库调整完成,开始更新结果数据");
        // 更新 结果数据
        outboundAdjustmentRecordService.updateBatchById(outboundAdjustmentRecordEntityList);
        log.info("其他出库调整完成");
    }
}
