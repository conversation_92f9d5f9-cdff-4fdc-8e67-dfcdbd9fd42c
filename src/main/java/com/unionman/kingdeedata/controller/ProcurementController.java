package com.unionman.kingdeedata.controller;

import com.unionman.kingdeedata.MaterialCacheUtil;
import com.unionman.kingdeedata.entitytmp.ProcurementAdjustmentRecordEntity;
import com.unionman.kingdeedata.service.ProcurementAdjustmentService;
import com.unionman.kingdeedata.service.PurchaseServiceKD;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "HDMI-采购数据调整", description = "采购相关数据调整-刘如")
@Slf4j
@RestController("/procurementController")
@RequiredArgsConstructor
public class ProcurementController {
    private final PurchaseServiceKD purchaseServiceKD;
    private final ProcurementAdjustmentService procurementAdjustmentService;
    private final MaterialCacheUtil materialCacheUtil;


    /**
     * 采购数据调整
     * 调整记录：2025/09/04 增加采购退料单
     */
    @GetMapping("/startProcurementAdjustment")
    @Operation(summary = "采购数据调整", description = "采购数据调整")
    public void startProcurementAdjustment() {
        // 获取所有需要调整的数据
        log.info("开始采购数据调整");
        List<ProcurementAdjustmentRecordEntity> procurementAdjustmentRecordEntities = procurementAdjustmentService.list();
        log.info("开始遍历数据,共{}条数据", procurementAdjustmentRecordEntities.size());
        // 获取所有数据金蝶 id
        for (ProcurementAdjustmentRecordEntity procurementAdjustmentRecordEntity : procurementAdjustmentRecordEntities) {
            // 根据物料缓存获取物料id
            Integer materialCodeToAdjustId = materialCacheUtil.getMaterialCache().get(procurementAdjustmentRecordEntity.getMaterialCodeToAdjust());
            Integer targetMaterialCodeId = materialCacheUtil.getMaterialCache().get(procurementAdjustmentRecordEntity.getTargetMaterialCode());
            if(materialCodeToAdjustId>0 && targetMaterialCodeId>0){
                // 设置物料id
                procurementAdjustmentRecordEntity.setMaterialCodeToAdjustId(Long.valueOf(materialCodeToAdjustId));
                procurementAdjustmentRecordEntity.setTargetMaterialCodeId(Long.valueOf(targetMaterialCodeId));
                // 根据订单编号+需替换的物料 Id 查询订单详情 id
                purchaseServiceKD.queryPurchaseEntryID(procurementAdjustmentRecordEntity);

                int count = 0;
                //查询完成进行更新
                try {
                    purchaseServiceKD.updatePurchaseOrderEntry(procurementAdjustmentRecordEntity);
                    count++;
                } catch (Exception e) {
                    log.error("更新采购订单数据失败：{}", e.getMessage());
                }
                try {
                    purchaseServiceKD.updatePurchaseRequisitionEntry(procurementAdjustmentRecordEntity);
                    count++;
                } catch (Exception e) {
                    log.error("更新采购申请数据失败：{}", e.getMessage());
                }
                try {
                    purchaseServiceKD.updatePurchaseReceiveEntry(procurementAdjustmentRecordEntity);
                    count++;
                } catch (Exception e) {
                    log.error("更新采购收料数据失败：{}", e.getMessage());
                }
                try {
                    purchaseServiceKD.updatePurchaseInStockEntry(procurementAdjustmentRecordEntity);
                    count++;
                } catch (Exception e){
                    log.error("更新采购入库数据失败：{}", e.getMessage());
                }
                try {
                    purchaseServiceKD.queryPurchaseBackStockEntry(procurementAdjustmentRecordEntity);
                    count++;
                } catch (Exception e){
                    log.error("更新采购退料数据失败：{}", e.getMessage());
                }

                if(count>4){
                    procurementAdjustmentRecordEntity.setFinalMessage("[成功]");
                }else{
                    procurementAdjustmentRecordEntity.setFinalMessage("[部分成功]：错误信息见不同调整项");
                }

            }else{
                procurementAdjustmentRecordEntity.setFinalMessage("[错误]:物料编码不存在"+procurementAdjustmentRecordEntity.getMaterialCodeToAdjust()+"->"+materialCodeToAdjustId+";"+procurementAdjustmentRecordEntity.getTargetMaterialCode()+"->"+targetMaterialCodeId);
            }
        }
        log.info("采购数据调整完成,开始更新结果数据");
        // 更新 结果数据
        procurementAdjustmentService.updateBatchById(procurementAdjustmentRecordEntities);
        log.info("采购数据调整完成");
    }
}
