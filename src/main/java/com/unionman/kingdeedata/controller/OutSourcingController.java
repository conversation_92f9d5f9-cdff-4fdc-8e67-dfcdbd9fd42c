package com.unionman.kingdeedata.controller;


import com.unionman.kingdeedata.MaterialCacheUtil;
import com.unionman.kingdeedata.entitytmp.OutsourcingMaterialAdjustmentEntity;
import com.unionman.kingdeedata.service.OutsourcingService;
import com.unionman.kingdeedata.servicetmp.OutsourcingAdjustmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "HDMI-委外用料数据调整", description = "委外用料数据调整-刘如")
@Slf4j
@RestController("/OutSourcingController")
@RequiredArgsConstructor
public class OutSourcingController {
    private final OutsourcingAdjustmentService outsourcingAdjustmentService;
    private final OutsourcingService outsourcingService;
    private final MaterialCacheUtil materialCacheUtil;


    @Operation(summary = "委外用料数据调整", description = "委外用料数据调整")
    @GetMapping("/startOutsourcingAdjustment")
    public void startOutsourcingAdjustment() {
        log.info("开始委外用料数据调整");
        List<OutsourcingMaterialAdjustmentEntity> outsourcingAdjustmentEntityList = outsourcingAdjustmentService.list();
        outsourcingAdjustmentEntityList.forEach(outsourcingAdjustmentEntity -> {
                // 查询委外用料明细
                Integer materialId = materialCacheUtil.getMaterialCache().get(outsourcingAdjustmentEntity.getSourceMaterialCode());
                Integer targetMaterialId = materialCacheUtil.getMaterialCache().get(outsourcingAdjustmentEntity.getTargetMaterialCode());
                if (materialId != null && targetMaterialId != null) {
                    outsourcingAdjustmentEntity.setSourceMaterialId(materialId);
                    outsourcingAdjustmentEntity.setTargetMaterialId(targetMaterialId);

                    try {
                        //查询委外用料单
                        outsourcingService.queryOutsourcingMaterialAndPick(outsourcingAdjustmentEntity);

                        // 更新委外用料
                        outsourcingService.updateOutsourcingEntry(outsourcingAdjustmentEntity);
                        //更新委外领料
                        outsourcingService.updateOutsourcingPickEntry(outsourcingAdjustmentEntity);
                    }catch( Exception ex ){

                        log.info("更新物料数据失败：{}", ex.getMessage(),ex);
                        outsourcingAdjustmentEntity.setResult("更新物料数据失败：" + ex.getMessage());
                    }
                }else{
                    outsourcingAdjustmentEntity.setResult("[错误]:物料编码不存在" + outsourcingAdjustmentEntity.getSourceMaterialCode() + "->" + materialId + ";" + outsourcingAdjustmentEntity.getTargetMaterialCode() + "->" + targetMaterialId);
                }
        });
        outsourcingAdjustmentService.saveOrUpdateBatch(outsourcingAdjustmentEntityList);
        log.info("委外用料数据调整完成");
    }
}
