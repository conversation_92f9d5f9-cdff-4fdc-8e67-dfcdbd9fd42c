package com.unionman.kingdeedata.controller;

import com.unionman.kingdeedata.entitytmp.SalesMachineTypeAdjustmentEntity;
import com.unionman.kingdeedata.mapper.kingdee.SalesOrderMapper;
import com.unionman.kingdeedata.service.SalesMachineTypeServiceKD;
import com.unionman.kingdeedata.servicetmp.SalesMachineTypeAdjustmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 销售机器类型调整控制器
 * <AUTHOR>
 */
@Tag(name = "HDMI-销售订单-销售机器类型调整", description = "销售订单、发货通知单、销售出库单机器类型调整")
@Slf4j
@RestController
@RequestMapping("/salesMachineType")
@RequiredArgsConstructor
public class SalesMachineTypeController {

    private final SalesMachineTypeAdjustmentService salesMachineTypeAdjustmentService;
    private final SalesMachineTypeServiceKD salesMachineTypeServiceKD; ;

    /**
     * 开始销售机器类型调整
     */
    @GetMapping("/startAdjustment")
    @Operation(summary = "销售机器类型调整", description = "调整销售订单、发货通知单、销售出库单的机器类型")
    public void startAdjustment() {
        log.info("开始销售机器类型调整");
        List<SalesMachineTypeAdjustmentEntity> adjustmentList = salesMachineTypeAdjustmentService.list();
        log.info("开始遍历数据,共{}条数据", adjustmentList.size());
        
        for (SalesMachineTypeAdjustmentEntity entity : adjustmentList) {
            try {
                // 更新销售订单机器类型
                salesMachineTypeServiceKD.updateSalesOrderMachineType(entity);
                
                // 更新发货通知单机器类型
                salesMachineTypeServiceKD.updateDeliveryNoticeMachineType(entity);
                
                // 更新销售出库单机器类型
                salesMachineTypeServiceKD.updateSalesOutboundMachineType(entity);
                
                // 设置最终结果
                entity.setFinalResult("处理完成");
                entity.setUpdatedAt(new Date());
                
                // 更新记录
                salesMachineTypeAdjustmentService.updateById(entity);
            } catch (Exception e) {
                log.error("处理异常: {}, 错误: {}", entity.getId(), e.getMessage(), e);
                entity.setFinalResult("处理异常: " + e.getMessage());
                salesMachineTypeAdjustmentService.updateById(entity);
            }
        }
        log.info("销售机器类型调整完成");
    }
}