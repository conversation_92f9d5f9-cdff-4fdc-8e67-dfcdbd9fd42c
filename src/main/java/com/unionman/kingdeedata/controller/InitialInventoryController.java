package com.unionman.kingdeedata.controller;

import com.unionman.kingdeedata.MaterialCacheUtil;
import com.unionman.kingdeedata.entitytmp.InitialInventoryAdjustmentEntity;
import com.unionman.kingdeedata.service.InitialInventoryServiceKD;
import com.unionman.kingdeedata.servicetmp.InitialInventoryAdjustmentRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "HDMI-初始库调整", description = "初始库数据调整-刘如")
@Slf4j
@RestController("/InitialInventoryController")
@RequiredArgsConstructor
public class InitialInventoryController {

    private final InitialInventoryAdjustmentRecordService initialInventoryAdjustmentRecordService;
    private final MaterialCacheUtil materialCacheUtil;
    private final InitialInventoryServiceKD initialInventoryServiceKD;


    @Operation(summary = "初始库存数据调整", description = "初始库存数据调整（2025年时，应无需调整）")
    @GetMapping("/startInitialInventoryAdjustment")
    public void startInitialInventoryAdjustment() {
        // 获取所有需要调整的数据
        log.info("开始初始库存数据调整");
        List<InitialInventoryAdjustmentEntity>  InitialInventoryAdjustmentEntities = initialInventoryAdjustmentRecordService.list();
        log.info("开始遍历数据,共{}条数据", InitialInventoryAdjustmentEntities.size());
        // 获取所有数据金蝶 id
        for (InitialInventoryAdjustmentEntity initialInventoryAdjustment : InitialInventoryAdjustmentEntities) {
            // 根据物料缓存获取物料id
            Integer materialCodeToAdjustId = materialCacheUtil.getMaterialCache().get(initialInventoryAdjustment.getMaterialCodeToAdjust());
            Integer targetMaterialCodeId = materialCacheUtil.getMaterialCache().get(initialInventoryAdjustment.getTargetMaterialCode());
            if (materialCodeToAdjustId > 0 && targetMaterialCodeId > 0) {
                // 设置物料id
                initialInventoryAdjustment.setMaterialCodeToAdjustId(Long.valueOf(materialCodeToAdjustId));
                initialInventoryAdjustment.setTargetMaterialCodeId(Long.valueOf(targetMaterialCodeId));
                // 根据订单编号+需替换的物料 Id 查询订单详情 id
                try {

                    // 先查询在更新  生产用料单
                    initialInventoryServiceKD.queryInitialInventoryEntryID(initialInventoryAdjustment);
                    // 更新
                    initialInventoryServiceKD.updateInitialInventoryByEntryID(initialInventoryAdjustment);

                } catch (Exception e) {
                    log.info("更新物料数据失败：{}", e.getMessage());
                    initialInventoryAdjustment.setInitialInventoryAdjustResult("更新物料数据失败：" + e.getMessage());
                }
            } else {
                initialInventoryAdjustment.setInitialInventoryAdjustResult("[错误]:物料编码不存在" + initialInventoryAdjustment.getMaterialCodeToAdjust() + "->" + materialCodeToAdjustId + ";" + initialInventoryAdjustment.getTargetMaterialCode() + "->" + targetMaterialCodeId);
            }
        }
        log.info("初始库存数据调整完成,开始更新结果数据");
        // 更新 结果数据
        initialInventoryAdjustmentRecordService.updateBatchById(InitialInventoryAdjustmentEntities);
        log.info("初始库存数据结果数据保存完成");
    }
}
