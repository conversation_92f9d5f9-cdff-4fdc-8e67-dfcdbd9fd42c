package com.unionman.kingdeedata.controller;

import com.unionman.kingdeedata.MaterialCacheUtil;
import com.unionman.kingdeedata.entitytmp.ProductMaterialAdjustmentEntity;
import com.unionman.kingdeedata.service.ProductMaterialServiceKD;
import com.unionman.kingdeedata.servicetmp.ProductMaterialAdjustmentRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "HDMI-生产用料清单", description = "生产用料清单-生产领料调整-刘如")
@Slf4j
@RestController("/productMaterialController")
@RequiredArgsConstructor
public class ProductMaterialController {

    private final ProductMaterialServiceKD productMaterialServiceKD;
    private final ProductMaterialAdjustmentRecordService productMaterialAdjustmentRecordService;
    private final MaterialCacheUtil materialCacheUtil;


    @Operation(summary = "生产用料清单调整", description = "生产用料清单调整")
    @GetMapping("/startProductMaterialAdjustment")
    public void startProductMaterialAdjustment() {
        // 获取所有需要调整的数据
        log.info("开始生产用料清单调整");
        List<ProductMaterialAdjustmentEntity> productMaterialAdjustmentEntities = productMaterialAdjustmentRecordService.list();
        log.info("开始遍历数据,共{}条数据", productMaterialAdjustmentEntities.size());
        // 获取所有数据金蝶 id
        for (ProductMaterialAdjustmentEntity productMaterialAdjustmentEntity : productMaterialAdjustmentEntities) {
            // 根据物料缓存获取物料id
            Integer materialCodeToAdjustId = materialCacheUtil.getMaterialCache().get(productMaterialAdjustmentEntity.getMaterialCodeToAdjust());
            Integer targetMaterialCodeId = materialCacheUtil.getMaterialCache().get(productMaterialAdjustmentEntity.getTargetMaterialCode());
            if(materialCodeToAdjustId>0 && targetMaterialCodeId>0) {
                // 设置物料id
                productMaterialAdjustmentEntity.setMaterialCodeToAdjustId(Long.valueOf(materialCodeToAdjustId));
                productMaterialAdjustmentEntity.setTargetMaterialCodeId(Long.valueOf(targetMaterialCodeId));
                // 根据订单编号+需替换的物料 Id 查询订单详情 id
                try {

                    // 先查询在更新  生产用料单
                    productMaterialServiceKD.queryProductMaterialEntryID(productMaterialAdjustmentEntity);
                    // 更新
                    productMaterialServiceKD.updateProductMaterialEntry(productMaterialAdjustmentEntity);

                    // 先查询 ## 领料单
                    productMaterialServiceKD.queryProductPickingMaterialEntryID(productMaterialAdjustmentEntity);
                    // 更新
                    productMaterialServiceKD.updateProductPickingMaterialEntry(productMaterialAdjustmentEntity);


                    // 查询+修改 退料单
                    productMaterialServiceKD.queryProductBackMaterialEntryID(productMaterialAdjustmentEntity);
                    productMaterialServiceKD.updateProductBackMaterialEntry(productMaterialAdjustmentEntity);

                }catch (Exception e){
                    log.info("更新物料数据失败：{}", e.getMessage());
                    productMaterialAdjustmentEntity.setFinalResult("更新物料数据失败："+ e.getMessage());
                }
            }else{
                productMaterialAdjustmentEntity.setFinalResult("[错误]:物料编码不存在"+productMaterialAdjustmentEntity.getMaterialCodeToAdjust()+"->"+materialCodeToAdjustId+";"+productMaterialAdjustmentEntity.getTargetMaterialCode()+"->"+targetMaterialCodeId);
            }
        }
        log.info("生产用料清单调整完成,开始更新结果数据");
        // 更新 结果数据
        productMaterialAdjustmentRecordService.updateBatchById(productMaterialAdjustmentEntities);
        log.info("生产用料清单调整完成");
    }
}
