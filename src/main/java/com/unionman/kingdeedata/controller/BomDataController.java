package com.unionman.kingdeedata.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.unionman.kingdeedata.entity.BomData;
import com.unionman.kingdeedata.excel.entity.BomExportRow;
import com.unionman.kingdeedata.excel.entity.ProductBomEntity;
import com.unionman.kingdeedata.excel.helper.BomExportHelper;
import com.unionman.kingdeedata.service.BomDataService;
import com.unionman.kingdeedata.service.BomExportRowService;
import com.unionman.kingdeedata.service.ProductBomService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * 查询金蝶数据控制器
 */
@Slf4j
@RestController("/bomdata")
@RequiredArgsConstructor
public class BomDataController {

    private final BomDataService bomDataService;

    private final BomExportRowService bomExportRowService;
    private final ProductBomService productBomService;

    /**
     * 获取bom 反查数据
     * @param response
     */
    @GetMapping("getBomDataExcel")
    public void getBomData(HttpServletResponse response)  {
        try{

            List<BomData> bomDataList= bomDataService.queryBomDataTree();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String excelfileName = URLEncoder.encode("bom芯片数据反查", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename="+excelfileName+";filename*=" + excelfileName + ".xlsx");

            List<BomExportRow> rows = exportBom(bomDataList,response.getOutputStream());
            // 导出数据并存储
            bomExportRowService.reSaveData(rows);

            log.info("转换成正查数据");
            List<ProductBomEntity> productBomEntityList = bomDataService.getBomListByExportRow(rows);
            productBomService.reSaveData(productBomEntityList);
            log.info("正查数据保存完成");
            // 整理成二份需要的数据
        }catch(Exception ex){
            log.info("反查导出数据异常");
            ex.printStackTrace();
        }
    }

    /**
     * 通过反查数据的结果 根据 产成品 查到所有的数据，并组装成实体类
     * @param data
     * @param out
     */

    private  List<BomExportRow> exportBom(List<BomData> data, OutputStream out) {
        List<BomExportRow> rows = BomExportHelper.buildExportRows(data);


        // 设置表头字段
//        String[] headers = {
//                "芯片编码", "芯片规格描述",
//                "贴片半成品编码", "贴片半成品描述",
//                "插件半成品编码", "插件半成品描述",
//                "整机虚拟件编码", "整机虚拟件描述",
//                "结构虚拟件编码", "结构虚拟件描述",
//                "包装虚拟件编码", "包装虚拟件描述",
//                "产成品编码", "产成品规格描述"
//        };

        ExcelWriter excelWriter = EasyExcel.write(out, BomExportRow.class).build();
        WriteSheet sheet = EasyExcel.writerSheet("bom 反查数据").build();

        // 注册单元格合并策略（合并芯片编码、芯片描述）
       // sheet.setCustomWriteHandler(new CellMergeStrategy(rows));

        excelWriter.write(rows, sheet);
        excelWriter.finish();
        return  rows;
    }


    /**
     * 正查数据导出， 关联的数据来自 反查bom  的产成品
     * 如果根据芯片查到没有产成品，那么就不会出现在这个表中
     *
     * 导出的数据中是按照此要求进行分类
     *            虚拟件 16
     *            半成品 13 14 （贴片半成品、插件半成品）
     *            ===========虚拟件/半成品列============
     *
     *            芯片 10.0240 开头
     *            ==========  芯片列 =============
     *            遥控器 10.065 开头
     *            ============遥控器列===========
     *            线材编码 编码为 12.0140、12.0180 12.0720
     *            =============线材列==========
     *            电池 10.045 开头
     *             ============电池列===========
     *
     *             端口  10.0203开头  并且 描述中带有 HMID端口字样
     *             ============端口列===========
     */


    @GetMapping("getBomDataExcelZC")
    public void getBomDataExcelZC(HttpServletResponse response)  {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String excelfileName = URLEncoder.encode("bom正查数据"+ LocalDate.now(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename="+excelfileName+".xlsx;filename*=" + excelfileName + ".xlsx");
           List<ProductBomEntity> productBomEntityList = productBomService.list();

            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), ProductBomEntity.class).build();
            WriteSheet sheet = EasyExcel.writerSheet("bom正查数据").build();
            excelWriter.write(productBomEntityList,sheet);

            excelWriter.finish();
        }catch (Exception ex){
            log.info("正查数据导出异常");
        }
    }

}
