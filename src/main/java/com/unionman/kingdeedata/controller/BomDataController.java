package com.unionman.kingdeedata.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.unionman.kingdeedata.MaterialCacheUtil;
import com.unionman.kingdeedata.entity.BomData;
import com.unionman.kingdeedata.excel.entity.BomExportRow;
import com.unionman.kingdeedata.excel.entity.BomMaterialReplaceEntity;
import com.unionman.kingdeedata.excel.entity.ProductBomEntity;
import com.unionman.kingdeedata.excel.helper.BomExportHelper;
import com.unionman.kingdeedata.service.BomDataService;
import com.unionman.kingdeedata.service.BomExportRowService;
import com.unionman.kingdeedata.service.BomMaterialReplaceService;
import com.unionman.kingdeedata.service.ProductBomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 查询金蝶数据控制器
 */
@Tag(name = "bomcontroller", description = "bom数据反查、正差、调整")
@Slf4j
@RestController("/bomdata")
@RequiredArgsConstructor
public class BomDataController {

    private final BomDataService bomDataService;

    private final BomExportRowService bomExportRowService;
    private final ProductBomService productBomService;
    private final BomMaterialReplaceService bomMaterialReplaceService;
    private final MaterialCacheUtil materialCacheUtil;


    /** ===========================  根据芯片进行反查      ================***/
    /**
     * 获取bom 反查数据
     *
     * @param response
     */
    @Operation(summary = "bom数据反查", description = "根据芯片型号反查至bom结构数据")
    @GetMapping("getBomDataExcel")
    public void getBomData(HttpServletResponse response) {
        try {

            List<BomData> bomDataList = bomDataService.queryBomDataTree();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String excelfileName = URLEncoder.encode("bom芯片数据反查", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + excelfileName + ";filename*=" + excelfileName + ".xlsx");

            List<BomExportRow> rows = exportBom(bomDataList, response.getOutputStream());
            // 导出数据并存储
            bomExportRowService.reSaveData(rows);


            // 整理成二份需要的数据
        } catch (Exception ex) {
            log.info("反查导出数据异常");
            ex.printStackTrace();
        }
    }

    /**
     * 通过反查数据的结果 根据 产成品 查到所有的数据，并组装成实体类
     *
     * @param data
     * @param out
     */
    private List<BomExportRow> exportBom(List<BomData> data, OutputStream out) {
        List<BomExportRow> rows = BomExportHelper.buildExportRows(data);

        ExcelWriter excelWriter = EasyExcel.write(out, BomExportRow.class).build();
        WriteSheet sheet = EasyExcel.writerSheet("bom 反查数据").build();

        // 注册单元格合并策略（合并芯片编码、芯片描述）
        // sheet.setCustomWriteHandler(new CellMergeStrategy(rows));

        excelWriter.write(rows, sheet);
        excelWriter.finish();
        return rows;
    }

    /** ===========================  反查数据导出 end    ================***/

    /**
     * 正查数据导出， 关联的数据来自 反查bom  的产成品
     * 如果根据芯片查到没有产成品，那么就不会出现在这个表中
     * <p>
     * 导出的数据中是按照此要求进行分类
     * 贴片半成品 13
     * =========================
     * 插件半成品 14
     * =========================
     * 整机虚拟件 16.003
     * =========================
     * 结构虚拟件 16.002
     * =========================
     * 包装虚拟件 16.001
     * =========================
     * <p>
     * 芯片 10.0240 开头
     * ==========  芯片列 =============
     * 遥控器 10.065 开头
     * ============遥控器列===========
     * 线材编码 编码为 12.0140、12.0180 12.0720
     * =============线材列==========
     * 电池 10.045 开头
     * ============电池列===========
     * <p>
     * 端口  10.0203开头  并且 描述中带有 HMID端口字样
     * ============端口列===========
     */
    @Operation(summary = "正查数据导出", description = "需要根据反查出来的结果(产品编码)再正查一次关键物料数据，并直接导出，需下载")
    @GetMapping("getBomDataExcelZC")
    public void getBomDataExcelZC(HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String excelfileName = URLEncoder.encode("bom正查数据" + LocalDate.now(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + excelfileName + ".xlsx;filename*=" + excelfileName + ".xlsx");
            log.info("查询正查数据根据正查数据计算反差数据");
            //要去重
            List<BomExportRow> rows = bomExportRowService.getAll();
            List<ProductBomEntity> productBomEntityList = bomDataService.getBomListByExportRow(rows);
            productBomService.reSaveData(productBomEntityList);
            log.info("正查数据保存完成");
            List<ProductBomEntity> productBomEntityListexport = productBomService.list();
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), ProductBomEntity.class).build();
            WriteSheet sheet = EasyExcel.writerSheet("bom正查数据").build();
            excelWriter.write(productBomEntityListexport, sheet);
            excelWriter.finish();
        } catch (Exception ex) {
            log.info("正查数据导出异常", ex);
        }
    }

    /** ===========================  替换bom 脚本   ================***/
    /**
     * 调整数据步骤 1：
     * 整理bom调整数据
     */
    @Operation(summary = "bom调整数据步骤 1：导入+整理数据 ID", description = "正反查询数据导出后，熊英整理总结出，哪些父物料下的物料需要调整，根据他提供的物料表先导入数据库，完成导入之后再点击步骤 1")
    @GetMapping("makBomChangeDataReady")
    public void makBomChangeData() {
        Map<String, Integer> allMaterialData = materialCacheUtil.getMaterialCache();
        log.info("huoqushuju {}", allMaterialData.size());
        bomMaterialReplaceService.queryBomMaterialReplaceDataAndChange(allMaterialData);
    }

    /**
     * 调整数据步骤 2
     * 开始调整数据
     */
    @Operation(summary = "bom调整数据步骤 2： 调整数据", description = "点击步骤 1 之后，根据导出的数据整理完成，等待 调整 ，点击步骤 2 之后，会开始调整数据，调整完成后会更新数据库")
    @GetMapping("startBomChangeData")
    public void startBomChangeData() {
        List<BomMaterialReplaceEntity> allBomMaterialReplaceData = bomMaterialReplaceService.queryAllBomMaterialReplaceReadyData();
        log.info("数据获取成功{}", allBomMaterialReplaceData.size());
        bomDataService.startReplaceDataByReady(allBomMaterialReplaceData);
        // 调整完成后需更新
        bomMaterialReplaceService.updateBatchById(allBomMaterialReplaceData);
    }

    /** ===========================  替换bom 脚本   end ================***/
}
