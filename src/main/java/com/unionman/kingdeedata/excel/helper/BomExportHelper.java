package com.unionman.kingdeedata.excel.helper;

import com.unionman.kingdeedata.entity.BomData;
import com.unionman.kingdeedata.excel.entity.BomExportRow;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

public class BomExportHelper {

    private static final Pattern P13 = Pattern.compile("^13.*");
    private static final Pattern P14 = Pattern.compile("^14.*");
    private static final Pattern P16003 = Pattern.compile("^16\\.003.*");
    private static final Pattern P16002 = Pattern.compile("^16\\.002.*");
    private static final Pattern P16001 = Pattern.compile("^16\\.001.*");
    private static final Pattern P17 = Pattern.compile("^17.*");

    public static List<BomExportRow> buildExportRows(List<BomData> rootList) {
        List<BomExportRow> result = new ArrayList<>();
        for (BomData root : rootList) {
            List<BomExportRow> rows = new ArrayList<>();
            fillRows(root, new BomExportRow(), rows);
            result.addAll(rows);
        }
        return result;
    }

    private static void fillRows(BomData node, BomExportRow baseRow, List<BomExportRow> result) {
        BomExportRow current = cloneRow(baseRow);
        String fn = node.getNnumber();
        String fs = node.getNspecification();
//        if(node.getNnumber().equals("17.0012.0071.99")){
//            System.out.println("目标");
//        }
        if(fn.startsWith("10")){
            current.setChipCode(fn);
            current.setChipSpec(fs);
        } else if (fn.startsWith("13")) {
            current.setPatchSemiCode(fn);
            current.setPatchSemiSpec(fs);
        } else if (fn.startsWith("14")) {
            current.setInsertSemiCode(fn);
            current.setInsertSemiSpec(fs);
        } else if (fn.startsWith("16.003")) {
            current.setVirtualWholeCode(fn);
            current.setVirtualWholeSpec(fs);
        }  else if (fn.startsWith("17")) {
            current.setFid(node.getNbomId());
            current.setProductCode(fn);
            current.setProductSpec(fs);
        }

        if(node.getChildBomData()!=null){
             if (node.getChildBomData().getNnumber().startsWith("16.002")) {
                current.setVirtualStructCode(node.getChildBomData().getNnumber());
                current.setVirtualStructSpec(node.getChildBomData().getNspecification());
            } else if (node.getChildBomData().getNnumber().startsWith("16.001")) {
                current.setVirtualPackageCode(node.getChildBomData().getNnumber());
                current.setVirtualPackageSpec(node.getChildBomData().getNspecification());
            }
        }



        if (node.getFatherBomData() == null || node.getFatherBomData().isEmpty()) {
            result.add(current);
        } else {
            for (BomData child : node.getFatherBomData()) {
                fillRows(child, current, result);
            }
        }
    }

    private static BomExportRow cloneRow(BomExportRow row) {
        BomExportRow copy = new BomExportRow();
        BeanUtils.copyProperties(row, copy);
        return copy;
    }
}
