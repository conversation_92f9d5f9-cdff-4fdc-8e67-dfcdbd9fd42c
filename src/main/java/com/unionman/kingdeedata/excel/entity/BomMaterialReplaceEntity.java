package com.unionman.kingdeedata.excel.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 根据父物料调整 子项物料
 * BOM物料替换实体类
 */
@Data
@TableName("bom_material_replace")
public class BomMaterialReplaceEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 父bom编码
     */
    private String fbomNumber;

    /**
     * 需要替换的物料编码_可能是多个/分隔符
     */
    private String replaceNumber;

    /**
     * 需要替换成的物料编码
     */
    private String targetNumber;

    /**
     * 多物料的情况下使用json 进行存储
     */

//    // 字段映射忽略
//    @TableField(exist = false)
//    private Map<String,Integer> replaceMateridsIdsMap;
//


    private String replaceMateridsIds;


    /**
     * 替换的物料编码 id
     */
    private Integer targetMaterid;

    /**
     * 错误信息
     */
    private String errorMessage;


    /**
     * 数据调整是否成功
     */
    private Boolean changed;

    /**
     * 更新信息
     */
    private String changeMessage;
}
