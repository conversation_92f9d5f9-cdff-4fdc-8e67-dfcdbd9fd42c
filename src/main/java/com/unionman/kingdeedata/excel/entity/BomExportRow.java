package com.unionman.kingdeedata.excel.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("bom_export_row")
public class BomExportRow {
    /**
     * 产成品 id
     */
    @ExcelIgnore
    private Integer fid;

    /**
     * 芯片
     */
    @ExcelProperty("芯片编码")
    private String chipCode;
    @ExcelProperty("芯片描述")
    private String chipSpec;

    /**
     * 贴片
     */
    @ExcelProperty("贴片半成品编码")
    private String patchSemiCode;
    @ExcelProperty("贴片半成品描述")
    private String patchSemiSpec;

    /**
     * 插件
     */
    @ExcelProperty("插件半成品编码")
    private String insertSemiCode;
    @ExcelProperty("插件半成品描述")
    private String insertSemiSpec;

    /**
     * 整机虚拟件
     */
    @ExcelProperty("整机虚拟件编码")
    private String virtualWholeCode;
    @ExcelProperty("整机虚拟件描述")
    private String virtualWholeSpec;

    /**
     * 结构虚拟件
     */
    @ExcelProperty("结构虚拟件编码")
    private String virtualStructCode;
    @ExcelProperty("结构虚拟件描述")
    private String virtualStructSpec;

    /**
     * 包装虚拟件
     */
    @ExcelProperty("包装虚拟件编码")
    private String virtualPackageCode;
    @ExcelProperty("包装虚拟件描述")
    private String virtualPackageSpec;

    /**
     * 产成品虚拟件
     */
    @ExcelProperty("产成品编码")
    private String productCode;
    @ExcelProperty("产成品描述")
    private String productSpec;

}
