package com.unionman.kingdeedata.excel.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 产品 BOM 实体类（用于 Excel 导出） 正查数据
 */
@Data
@TableName("product_bom")
public class ProductBomEntity {

    @ExcelProperty("产成品BOM编码")
    private String productBomCode="";

    @ExcelProperty("产成品规格描述")
    private String finishedProductSpec="";

    @ExcelProperty("贴片盘成品编码")
    private String semiProductCode="";

    @ExcelProperty("贴片半成品描述")
    private String semiProductSpec="";

    @ExcelProperty("插件半成品编码")
    private String insertSemiCode="";

    @ExcelProperty("插件半成品描述")
    private String insertSemiSpec="";

    @ExcelProperty("整机虚拟件编码")
    private String virtualWholeCode="";

    @ExcelProperty("整机虚拟件描述")
    private String virtualWholeSpec="";

    @ExcelProperty("结构虚拟件编码")
    private String virtualStructCode="";

    @ExcelProperty("结构虚拟件描述")
    private String virtualStructSpec="";

    @ExcelProperty("包装虚拟件编码")
    private String virtualPackageCode="";

    @ExcelProperty("包装虚拟件描述")
    private String virtualPackageSpec="";


    @ExcelProperty("芯片编码")
    private String chipCode="";

    @ExcelProperty("芯片规格描述")
    private String chipSpec="";

    @ExcelProperty("遥控器编码")
    private String remoteControllerCode="";

    @ExcelProperty("遥控器描述")
    private String remoteControllerDesc="";

    @ExcelProperty("线材编码")
    private String wireCode="";

    @ExcelProperty("线材描述")
    private String wireDesc="";

    @ExcelProperty("端口编码")
    private String portCode="";

    @ExcelProperty("端口描述")
    private String portDesc="";

    @ExcelProperty("电池编码")
    private String batteryCode="";

    @ExcelProperty("电池描述")
    private String batteryDesc="";

    @ExcelProperty("插件HDMI编码")
    private String insertHdmiCode="";

    @ExcelProperty("插件HDMI描述")
    private String insertHdmiDesc="";

    @ExcelProperty("贴片HDMI编码")
    private String patchHdmiCode="";

    @ExcelProperty("贴片HDMI描述")
    private String patchHdmiDesc="";
}
