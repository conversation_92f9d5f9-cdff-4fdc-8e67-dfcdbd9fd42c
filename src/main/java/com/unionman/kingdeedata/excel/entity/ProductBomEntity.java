package com.unionman.kingdeedata.excel.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 产品 BOM 实体类（用于 Excel 导出）
 */
@Data
@TableName("product_bom")
public class ProductBomEntity {

    @ExcelProperty("产成品BOM编码")
    private String productBomCode="";

    @ExcelProperty("产成品规格描述")
    private String finishedProductSpec="";

    @ExcelProperty("半成品/虚拟件编码")
    private String semiProductCode="";

    @ExcelProperty("半成品/虚拟件规格描述")
    private String semiProductSpec="";

    @ExcelProperty("芯片编码")
    private String chipCode="";

    @ExcelProperty("芯片规格描述")
    private String chipSpec="";

    @ExcelProperty("遥控器编码")
    private String remoteControllerCode="";

    @ExcelProperty("遥控器描述")
    private String remoteControllerDesc="";

    @ExcelProperty("线材编码")
    private String wireCode="";

    @ExcelProperty("线材描述")
    private String wireDesc="";

    @ExcelProperty("端口编码")
    private String portCode="";

    @ExcelProperty("端口描述")
    private String portDesc="";

    @ExcelProperty("电池编码")
    private String batteryCode="";

    @ExcelProperty("电池描述")
    private String batteryDesc="";
}
