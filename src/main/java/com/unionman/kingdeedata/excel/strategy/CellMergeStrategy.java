package com.unionman.kingdeedata.excel.strategy;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.unionman.kingdeedata.excel.entity.BomExportRow;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.List;

public class CellMergeStrategy extends AbstractMergeStrategy {
    private final List<BomExportRow> data;

    public CellMergeStrategy(List<BomExportRow> data) {
        this.data = data;
    }

//    @Override
//    public void merge(CellMergeStrategyContext context) {
//        int rowIndex = context.getRowIndex();
//        if (rowIndex == 0) return; // 跳过表头
//
//        Sheet sheet = context.getWriteSheetHolder().getSheet();
//        String currentKey = getChipKey(data.get(rowIndex - 1));
//
//        int startRow = rowIndex;
//        while (rowIndex < data.size() &&
//                getChipKey(data.get(rowIndex - 1)).equals(currentKey)) {
//            rowIndex++;
//        }
//
//        // 合并芯片编码和芯片描述两列
//        for (int col = 0; col <= 1; col++) {
//            sheet.addMergedRegion(new CellRangeAddress(
//                    startRow, rowIndex - 1, col, col
//            ));
//        }
//    }

    private String getChipKey(BomExportRow row) {
        return row.getChipCode() + "#" + row.getChipSpec();
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer integer) {

    }
}
