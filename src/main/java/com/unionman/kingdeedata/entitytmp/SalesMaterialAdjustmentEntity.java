package com.unionman.kingdeedata.entitytmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 销售出库单物料调整实体类
 * <AUTHOR>
 */
@Data
@TableName("sales_material_adjustment")
public class SalesMaterialAdjustmentEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 销售出库单单号
     */
    private String salesOutboundCode;
    @TableField(exist = false)
    private List<Long> salesOutboundEntryIds;

    /**
     * 销售出库单调整结果
     */
    private String salesOutboundAdjustResult;

    /**
     * 发货通知单单号
     */
    private String deliveryNoticeCode;
    @TableField(exist = false)
    private List<Long> deliveryNoticeEntryIds;

    /**
     * 发货通知单调整结果
     */
    private String deliveryNoticeAdjustResult;

    /**
     * 需调整物料编码
     */
    private String materialCodeToAdjust;

    /**
     * 需调整物料编码ID
     */
    private Long materialCodeToAdjustId;

    /**
     * 目标物料编码
     */
    private String targetMaterialCode;

    /**
     * 目标物料编码ID
     */
    private Long targetMaterialCodeId;

    /**
     * 最后结果
     */
    private String finalResult;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
