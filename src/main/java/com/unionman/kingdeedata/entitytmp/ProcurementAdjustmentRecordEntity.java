package com.unionman.kingdeedata.entitytmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TableName("procurement_adjustment_records")
public class ProcurementAdjustmentRecordEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 采购申请
     */
    private String purchaseApplicationNo;
    /**
     * 采购申请符合调整条件的明细行 ID
     */
    @TableField(exist = false)
    private List<Long> purchaseApplicationEntryIds;
    /**
     * 采购申请调整结果
     */
    private String purchaseApplicationAdjustResult;

    /**
     * 采购订单
     */
    private String purchaseOrderNo;
    /**
     * 采购订单符合调整条件的明细行 ID
     */
    @TableField(exist = false)
    private List<Long> purchaseOrderEntryIds;

    /**
     * 采购订单调整结果
     */
    private String purchaseOrderAdjustResult;

    /**
     * 采购收料单号
     */
    private String purchaseReceiptNo;
    /**
     * 采购收料单符合调整条件的明细行 ID
     */
    @TableField(exist = false)
    private List<Long> purchaseReceiptEntryIds;
    /**
     * 采购收料单调整结果
     */
    private String purchaseReceiptAdjustResult;

    /**
     * 采购入库单号
     */
    private String purchaseWarehouseInNo;
    /**
     * 采购入库单符合调整条件的明细行 ID
     */
    @TableField(exist = false)
    private List<Long> purchaseWarehouseInEntryIds;
    /**
     *  采购入库单调整结果
     */
    private String purchaseWarehouseInAdjustResult;

    /**
     * 采购退料单号
     */
    private String purchaseBackNo;
    /**
     * 采购退料单符合调整条件的明细行 ID
     */
    @TableField(exist = false)
    private List<Long> purchaseBackEntryIds;
    /**
     * 采购退料单调整结果
     */
    private String purchaseBackAdjustResult;


    /**
     * 需要调整的物料编码
     */
    private String materialCodeToAdjust;

    /**
     * 需要调整的物料 ID
     */
    private Long materialCodeToAdjustId;

    /**
     * 目标物料编码
     */
    private String targetMaterialCode;

    /**
     * 目标物料 ID
     */
    private Long targetMaterialCodeId;

    /**
     *
     */
    private String finalMessage;


    private Date createdAt;

    private Date updatedAt;

}
