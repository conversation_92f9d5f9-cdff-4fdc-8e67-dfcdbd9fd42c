package com.unionman.kingdeedata.entitytmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 委外用料数据调整表实体类
 * <AUTHOR>
 */
@Data
@TableName("outsourcing_material_adjustment")
public class OutsourcingMaterialAdjustmentEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 委外用料单号
     */
    private String outsourcingMaterialNo;
    /**
     * 委外用料单符合调整条件的明细行ID
     */
    @TableField(exist = false)
    private List<Long> outsourcingMaterialEntryIds;
    
    /**
     * 委外用料单调整结果
     */
    private String outsourcingMaterialAdjustResult;
    
    /**
     * 委外领料单号
     */
    private String outsourcingPickNo;

    /**
     * 委外领料单符合调整条件的明细行ID
     */
    @TableField(exist = false)
    private List<Long> outsourcingPickEntryIds;

    /**
     * 委外领料单调整结果
     */
    private String outsourcingPickAdjustResult;
    
    /**
     * 需调整物料编码
     */
    private String sourceMaterialCode;
    
    /**
     * 需调整物料编码ID
     */
    private Integer sourceMaterialId;
    
    /**
     * 目标物料编码
     */
    private String targetMaterialCode;
    
    /**
     * 目标物料编码ID
     */
    private Integer targetMaterialId;
    
    /**
     * 最终结果
     */
    private String result;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    

}
