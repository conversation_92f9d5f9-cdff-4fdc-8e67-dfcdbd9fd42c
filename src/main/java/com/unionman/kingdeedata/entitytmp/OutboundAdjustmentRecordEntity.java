package com.unionman.kingdeedata.entitytmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * 出库单调整记录
 */
@Data
@TableName("outstock_adjustment_records")
public class OutboundAdjustmentRecordEntity {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 申请单号
     */
    private String applicationNo;
    /**
     * 申请单符合调整条件的明细行 ID
     */
    @TableField(exist = false)
    private List<Long> applicationNoEntryIds;
    /**
     * 申请单调整结果
     */
    private String applicationAdjustResult;
    /**
     * 其他出库单号
     */
    private String otherOutboundNo;

    /**
     * 其他出库单符合调整条件的明细行 ID
     */
    @TableField(exist = false)
    private List<Long> otherOutboundEntryIds;
    /**
     * 其他出库单调整结果
     */
    private String otherOutboundAdjustResult;
    /**
     * 需要调整的物料编码
     */
    private String materialCodeToAdjust;
    /**
     * 需要调整的物料编码 ID
     */
    private Long materialCodeToAdjustId;
    /**
     * 目标物料编码
     */
    private String targetMaterialCode;
    /**
     * 目标物料编码 ID
     */
    private Long targetMaterialCodeId;
    private Date createdAt;
    private Date updatedAt;
    /**
     * 最终结果
     */
    private String finalResult;
}
