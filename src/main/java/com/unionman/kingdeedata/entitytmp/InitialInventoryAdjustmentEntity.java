package com.unionman.kingdeedata.entitytmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 初始库存表调整实体
 */
@Data
@TableName("initial_inventory_adjustment")
public class InitialInventoryAdjustmentEntity {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 初始库存单号
     */
    private String initialInventoryNo;
    /**
     * 初始库存单符合调整条件的明细行ID
     */
    @TableField(exist = false)
    private List<Long> initialInventoryEntryIds;
    /**
     * 待调整的物料编码
     */
    private String materialCodeToAdjust;
    /**
     * 待调整的物料编码ID
     */
    private Long materialCodeToAdjustId;
    /**
     * 目标物料编码
     */
    private String targetMaterialCode;
    /**
     * 目标物料编码ID
     */
    private Long targetMaterialCodeId;
    private String initialInventoryAdjustResult;
    private Date createdAt;
    private Date updatedAt;

}
