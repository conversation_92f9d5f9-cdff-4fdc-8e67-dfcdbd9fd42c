package com.unionman.kingdeedata.entitytmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 销售机器类型调整
 * <AUTHOR>
 */
@Data
@TableName("sales_machine_type_adjustment")
public class SalesMachineTypeAdjustmentEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 销售订单编码
     */
    private String salesOrderCode;
    @TableField(exist = false)
    private Long salesOrderFid;

    /**
     * 销售订单调整结果
     */
    private String salesOrderAdjustResult;

    /**
     * 发货通知单编码
     */
    private String deliveryNoticeCode;
    @TableField(exist = false)
    private Long deliveryNoticeFid;

    /**
     * 发货通知单调整结果
     */
    private String deliveryNoticeAdjustResult;

    /**
     * 销售出库单
     */
    private String salesOutboundCode;
    @TableField(exist = false)
    private Long salesOutboundFid;

    /**
     * 销售出库单调整结果
     */
    private String salesOutboundAdjustResult;

    /**
     * 原本机器类型名称
     */
    private String originalMachineType;

    /**
     * 调整机器类型名称
     */
    private String targetMachineType;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 最后结果
     */
    private String finalResult;
}