package com.unionman.kingdeedata.entitytmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 生产用料单数据调整表
 */
@Data
@TableName("production_material_adjustment")
public class ProductMaterialAdjustmentEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 生产用料单号
     */
    private String productionOrderNo;

    /**
     * 生产用料单符合调整条件的明细行ID
     */
    @TableField(exist = false)
    private List<Long> productionOrderEntryIds;

    /**
     * 生产用料单调整结果
     */
    private String productionOrderAdjustResult;

    /**
     * 生产领料单号
     */
    private String productionPickingNo;
    /**
     * 生产领料单符合调整条件的明细行ID
     */
    @TableField(exist = false)
    private List<Long> productionPickingEntryIds;

    /**
     * 生产领料单调整结果
     */
    private String productionPickingAdjustResult;

    /**
     * 退料单
     */
    private String productionBackNo;
    /**
     * 退料单符合调整条件的明细行ID
     */
    @TableField(exist = false)
    private List<Long> productionBackEntryIds;
    /**
     * 退料单调整结果
     */
    private String productionBackAdjustResult;


    /**
     * 需调整物料编码
     */
    private String materialCodeToAdjust;

    /**
     * 需调整物料编码ID
     */
    private Long materialCodeToAdjustId;

    /**
     * 目标物料编码
     */
    private String targetMaterialCode;

    /**
     * 目标物料编码ID
     */
    private Long targetMaterialCodeId;

    /**
     * 最后结果
     */
    private String finalResult;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
