package com.unionman.kingdeedata;

import com.unionman.kingdeedata.entity.BomDateZC;
import com.unionman.kingdeedata.mapper.kingdee.BomDataMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 物料数据缓存（近 5 万条）
 * 根据物料编码 查询到的物料数据id
 * 只包含九联组织
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class MaterialCacheUtil {

    private final BomDataMapper bomDataMapper;

    private Map<String, Integer> materialCache;

    public Map<String, Integer> getMaterialCache() {
        if (materialCache == null || materialCache.isEmpty()) {
            materialCache = new HashMap<>();
            List<BomDateZC> bomDataList = bomDataMapper.selectAllMaterial();
            log.info("缓存所有物料数据：{}-只包含九联组织", bomDataList.size());
            for (BomDateZC bomData : bomDataList) {
                materialCache.put(bomData.getFnumber(), bomData.getFmaterialId());
            }
        }
        return materialCache;
    }
}
