package com.unionman.kingdeedata.servicetmp.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unionman.kingdeedata.entitytmp.OutsourcingMaterialAdjustmentEntity;
import com.unionman.kingdeedata.mapper.temp.OutsourcingAdjustmentRecordMapper;
import com.unionman.kingdeedata.servicetmp.OutsourcingAdjustmentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@DS("tempdb")
@Transactional
public class OutsourcingAdjustmentServiceImpl extends ServiceImpl<OutsourcingAdjustmentRecordMapper, OutsourcingMaterialAdjustmentEntity> implements OutsourcingAdjustmentService {
}
