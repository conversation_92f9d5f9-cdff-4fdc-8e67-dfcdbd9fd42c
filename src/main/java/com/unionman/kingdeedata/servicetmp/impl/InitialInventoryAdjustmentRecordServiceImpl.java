package com.unionman.kingdeedata.servicetmp.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unionman.kingdeedata.entitytmp.InitialInventoryAdjustmentEntity;
import com.unionman.kingdeedata.mapper.temp.InitialInventoryAdjuestmentRecordMapper;
import com.unionman.kingdeedata.servicetmp.InitialInventoryAdjustmentRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@DS("tempdb")
@Transactional
public class InitialInventoryAdjustmentRecordServiceImpl extends ServiceImpl<InitialInventoryAdjuestmentRecordMapper, InitialInventoryAdjustmentEntity> implements InitialInventoryAdjustmentRecordService {
}
