package com.unionman.kingdeedata.servicetmp.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unionman.kingdeedata.entitytmp.OutboundAdjustmentRecordEntity;
import com.unionman.kingdeedata.mapper.temp.OutboundAdjustmentRecordMapper;
import com.unionman.kingdeedata.servicetmp.OutboundAdjustmentRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
@DS("tempdb")
public class OutboundAdjustmentRecordServiceImpl extends ServiceImpl<OutboundAdjustmentRecordMapper, OutboundAdjustmentRecordEntity> implements OutboundAdjustmentRecordService {
}
