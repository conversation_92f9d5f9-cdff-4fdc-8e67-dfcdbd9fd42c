package com.unionman.kingdeedata.servicetmp.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unionman.kingdeedata.entitytmp.SalesMachineTypeAdjustmentEntity;
import com.unionman.kingdeedata.mapper.temp.SalesMachineTypeAdjustmentMapper;
import com.unionman.kingdeedata.servicetmp.SalesMachineTypeAdjustmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 销售机器类型调整服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@DS("tempdb")
@Transactional
public class SalesMachineTypeAdjustmentServiceImpl extends ServiceImpl<SalesMachineTypeAdjustmentMapper, SalesMachineTypeAdjustmentEntity> implements SalesMachineTypeAdjustmentService {


}