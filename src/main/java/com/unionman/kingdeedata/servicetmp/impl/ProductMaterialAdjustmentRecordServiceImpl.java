package com.unionman.kingdeedata.servicetmp.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unionman.kingdeedata.entitytmp.ProductMaterialAdjustmentEntity;
import com.unionman.kingdeedata.mapper.temp.ProductmentMaterialAdjustmentRecordMapper;
import com.unionman.kingdeedata.servicetmp.ProductMaterialAdjustmentRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@DS("tempdb")
@Transactional
public class ProductMaterialAdjustmentRecordServiceImpl extends ServiceImpl<ProductmentMaterialAdjustmentRecordMapper, ProductMaterialAdjustmentEntity> implements ProductMaterialAdjustmentRecordService {
}
