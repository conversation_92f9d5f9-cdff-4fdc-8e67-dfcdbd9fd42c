package com.unionman.kingdeedata.mapper.kingdee;

import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * 采购订单相关数据表
 * 不直接映射表通过sql 调整无需实体
 *
 */
@Repository
@Mapper
public interface ProductMaterialMapper {

    /**
     * 查询生产用料清单
     * @param billNo 单据编号
     * @param materialId 用料编号
     * @return
     */
    List<BaseMainEntryIdEntity> queryProductMaterial(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updateProductMaterialEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);

    /**
     * 查询生产领料单
     * @param billNo 单据编号
     * @param materialId 用料编号
     * @return
     */
    List<BaseMainEntryIdEntity> queryProductPickingMaterial(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updateProductPickingMaterialEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);


    /**
     * 查询生产退料单
     * @param billNo 单据编号
     * @param materialId 用料编号
     * @return
     */
    List<BaseMainEntryIdEntity> queryProductBackMaterial(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updateProductBackMaterialEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);
}
