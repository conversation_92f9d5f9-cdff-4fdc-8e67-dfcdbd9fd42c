package com.unionman.kingdeedata.mapper.kingdee;

import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
@Mapper
public interface InitialInventoryMapper {
    List<BaseMainEntryIdEntity> queryInitialInventoryMaterial(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updateInitialInventoryEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);
}
