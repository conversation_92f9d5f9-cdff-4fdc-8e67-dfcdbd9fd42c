package com.unionman.kingdeedata.mapper.kingdee;

import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * 销售订单相关数据表
 */
@Repository
@Mapper
public interface SalesOrderMapper {
    
    /**
     * 查询销售订单
     * @param billNo 单据编号
     * @return 销售订单基础信息
     */
    List<BaseMainEntryIdEntity> querySalesOrder(@Param("billNo") String billNo);
    
    /**
     * 更新销售订单机器类型
     * @param fid 单据ID
     * @param machineType 机器类型
     * @return 影响行数
     */
    int updateSalesOrderMachineType(@Param("fid") Long fid, @Param("machineType") String machineType);
    
    /**
     * 查询发货通知单
     * @param billNo 单据编号
     * @return 发货通知单基础信息
     */
    List<BaseMainEntryIdEntity> queryDeliveryNotice(@Param("billNo") String billNo);
    
    /**
     * 更新发货通知单机器类型
     * @param fid 单据ID
     * @param machineType 机器类型
     * @return 影响行数
     */
    int updateDeliveryNoticeMachineType(@Param("fid") Long fid, @Param("machineType") String machineType);
    
    /**
     * 查询销售出库单
     * @param billNo 单据编号
     * @return 销售出库单基础信息
     */
    List<BaseMainEntryIdEntity> querySalesOutstock(@Param("billNo") String billNo);
    
    /**
     * 更新销售出库单机器类型
     * @param fid 单据ID
     * @param machineType 机器类型
     * @return 影响行数
     */
    int updateSalesOutstockMachineType(@Param("fid") Long fid, @Param("machineType") String machineType);

    /**
     * 根据物料编号查询销售出库单明细id
     * @param billNo 单据编号
     * @param materialId 物料ID
     * @return 销售出库单明细id
     */
    List<BaseMainEntryIdEntity> querySalesOutstockEntryId(@Param("billNo") String billNo, @Param("materialId") Long materialId);

    /**
     * 更新销售出库单明细物料
     * @param targetMaterialId 目标物料ID
     * @param entryIds 明细ID列表
     * @return 影响行数
     */
    int updateSalesOutstockEntry(@Param("targetMaterialId") Long targetMaterialId, @Param("entryIds") List<Long> entryIds);
}