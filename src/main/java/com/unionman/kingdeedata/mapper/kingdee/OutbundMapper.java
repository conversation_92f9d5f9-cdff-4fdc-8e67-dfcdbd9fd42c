package com.unionman.kingdeedata.mapper.kingdee;

import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface OutbundMapper {
    // 查询出库申请单
    List<BaseMainEntryIdEntity> queryOutStockApply(@Param("billNo") String billNo,
                                                   @Param("materialId") Long materialId);

    // 更新出库申请单明细
    int updateOutStockApplyEntry(@Param("targetMaterialId") Long targetMaterialId,
                                 @Param("entryIds") List<Long> entryIds);

    // 查询其他出库单
    List<BaseMainEntryIdEntity> queryOtherOutStock(@Param("billNo") String billNo,
                                                   @Param("materialId") Long materialId);

    // 更新其他出库单明细
    int updateOtherOutStockEntry(@Param("targetMaterialId") Long targetMaterialId,
                                 @Param("entryIds") List<Long> entryIds);
}
