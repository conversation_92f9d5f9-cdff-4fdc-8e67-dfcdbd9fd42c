package com.unionman.kingdeedata.mapper.kingdee;

import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * 采购订单相关数据表
 * 不直接映射表通过sql 调整无需实体
 *
 */
@Repository
@Mapper
public interface ProcurementOrderMapper {
    List<BaseMainEntryIdEntity> queryPurchaseRequisition(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updatePurchaseRequisitionEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);

    List<BaseMainEntryIdEntity> queryPurchaseOrder(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updatePurchaseOrderEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);

    List<BaseMainEntryIdEntity> queryPurchaseReceive(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updatePurchaseReceiveEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);

    List<BaseMainEntryIdEntity> queryPurchaseInStock(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updatePurchaseInStockEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);

    // 采购退料单查询及修改
    List<BaseMainEntryIdEntity> queryPurchaseBackStock(@Param("billNo") String billNo, @Param("materialId") Long materialId);
    int updatePurchaseBackStockEntry(@Param("targetMaterialId") Long targetMaterialId,  @Param("entryIds") List<Long> entryIds);
}
