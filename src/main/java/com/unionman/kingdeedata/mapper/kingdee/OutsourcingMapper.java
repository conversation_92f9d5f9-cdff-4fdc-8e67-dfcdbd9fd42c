package com.unionman.kingdeedata.mapper.kingdee;

import com.unionman.kingdeedata.entity.BaseMainEntryIdEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 委外相关 Mapper 接口
 */
@Mapper
@Repository
public interface OutsourcingMapper {
    
    /**
     * 查询委外用料明细
     * @param billNo 单据编号
     * @param materialId 物料ID
     * @return 匹配的记录列表
     */
    List<BaseMainEntryIdEntity> queryOutsourcingMaterial(@Param("billNo") String billNo, @Param("materialId") Integer materialId);
    
    /**
     * 更新委外用料条目
     * @param targetMaterialId 目标物料ID
     * @param entryIds 需要更新的条目ID列表
     */
    void updateOutsourcingEntry(@Param("targetMaterialId") Integer targetMaterialId, @Param("entryIds") List<Long> entryIds);
    
    /**
     * 查询委外领料明细
     * @param billNo 单据编号
     * @param materialId 物料ID
     * @return 匹配的记录列表
     */
    List<BaseMainEntryIdEntity> queryOutsourcingPack(@Param("billNo") String billNo, @Param("materialId") Integer materialId);
    
    /**
     * 更新委外领料条目
     * @param targetMaterialId 目标物料ID
     * @param entryIds 需要更新的条目ID列表
     */
    void updateOutsourcingPickEntry(@Param("targetMaterialId") Integer targetMaterialId, @Param("entryIds") List<Long> entryIds);
}
