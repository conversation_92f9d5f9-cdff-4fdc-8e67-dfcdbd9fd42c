package com.unionman.kingdeedata.mapper.kingdee;

import com.unionman.kingdeedata.entity.BomChild;
import com.unionman.kingdeedata.entity.BomData;
import com.unionman.kingdeedata.entity.BomDateZC;
import com.unionman.kingdeedata.entity.MaterialLEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
@Mapper
public interface BomDataMapper {

    /**
     * 查询bom数据
      */
    public List<BomData> queryBomDataByMaterialID(@Param("materialids") Set<Integer> materialids);


    /**
     * 根据物料编码查询物料编码及物料描述
     */
    public List<BomData> queryMaterialData(@Param("fnumbers")List<String> fnumbers);
    public List<BomData> queryBomChildByFatherBomIdBomTable(@Param("bomIds")Set<Integer> bomIds, @Param("fnumber")String fnumber);
    public List<BomData> queryBomChildByFatherBomIdMaterialTable(@Param("bomIds")Set<Integer> bomIds, @Param("fnumber")String fnumber);
    public List<BomDateZC> selectBomChildInfo(@Param("bomid")Integer bomid);
    public List<BomDateZC> selectAllMaterial();
    public List<BomChild> queryBomDataByFnumberAndChildMaterialId(@Param("bnumber") String bnumber, @Param("materialIds")Set<Integer> materialIds);
    //deleteBomChildByEntryId
    public void deleteBomChildByEntryId(@Param("entryIds")Set<Integer> entryIds);
    //updateBomChildMaterialId
    public void updateBomChildMaterialIdByEntryId(@Param("entryId")Integer entryId, @Param("materialId")Integer materialId);
    // 查询物料描述表
    public List<MaterialLEntity> queryMaterialDescription();
    // 更新物料描述表
    public void updateMaterialDescription(@Param("fpkid")Long materialId, @Param("specification")String materialDescription);


}
