package com.unionman.kingdeedata.mapper.kingdee;

import com.unionman.kingdeedata.entity.BomData;
import com.unionman.kingdeedata.entity.BomDateZC;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
@Mapper
public interface BomDataMapper {

    /**
     * 查询bom数据
      */
    public List<BomData> queryBomDataByMaterialID(@Param("materialids") Set<Integer> materialids);


    /**
     * 根据物料编码查询物料编码及物料描述
     */
    public List<BomData> queryMaterialData(@Param("fnumbers")List<String> fnumbers);
    public List<BomData> queryBomChildByFatherBomIdBomTable(@Param("bomIds")Set<Integer> bomIds, @Param("fnumber")String fnumber);
    public List<BomData> queryBomChildByFatherBomIdMaterialTable(@Param("bomIds")Set<Integer> bomIds, @Param("fnumber")String fnumber);
    public List<BomDateZC> selectBomChildInfo(@Param("bomid")Integer bomid);
}
