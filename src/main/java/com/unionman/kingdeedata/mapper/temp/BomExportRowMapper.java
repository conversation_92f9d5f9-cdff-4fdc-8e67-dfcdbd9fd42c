package com.unionman.kingdeedata.mapper.temp;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.unionman.kingdeedata.excel.entity.BomExportRow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface BomExportRowMapper extends BaseMapper<BomExportRow> {

    // 查询不重复的产成品所有数据
    @Select("SELECT DISTINCT fid, product_code, product_spec FROM bom_export_row where fid is not null")
    List<BomExportRow> selectDistinctProductData();
}
