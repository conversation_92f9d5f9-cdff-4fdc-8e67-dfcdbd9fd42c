package com.unionman.kingdeedata.entity;

import lombok.Data;

import java.util.List;

@Data
public class BomData {
    /**
     * 新物料bomID
     */
    private  Integer nbomId;
    /**
     *  新物料编码
     */
    private String nnumber;
    /**
     *  新物料物料 ID
     */
    private Integer  nmaterialId ;
    /**
     *  新物料描述
     */
    private String  nspecification;

    /**
     *  bom明细用到的 物料 ID
     */
    private Integer cmaterialId;
    /**
     *  bom 明细用到的 bomID - 当时带version 的bom是，是使用 bomID 进行关联
     */
    private Integer cbomId;

    private List<BomData> fatherBomData;

    /**
     * 兄弟层次用于存储 对应的虚拟件
     * // 通过父根据传入的同级别的 bomID /物料 ID进行关联
     */
    private BomData childBomData;

}
