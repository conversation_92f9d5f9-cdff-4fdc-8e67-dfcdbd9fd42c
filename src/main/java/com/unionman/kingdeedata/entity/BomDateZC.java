package com.unionman.kingdeedata.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;

/**
 *  正查表 名字不好取了 用这个
 */
@Data
public class BomDateZC {
    @ExcelProperty("子项ID(FID)")
    private Integer fid;

    @ExcelProperty("BOM ID(FBOMID)")
    private Integer fbomId;

    @ExcelProperty("物料编码(FNUMBER)")
    private String fnumber;

    @ExcelProperty("物料ID(FMATERIALID)")
    private Integer fmaterialId;

    @ExcelProperty("规格描述(FSPECIFICATION)")
    private String fspecification;

    private List<BomDateZC> bomChild;
}
