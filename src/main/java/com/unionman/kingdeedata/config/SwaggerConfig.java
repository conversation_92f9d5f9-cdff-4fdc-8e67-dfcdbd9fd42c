package com.unionman.kingdeedata.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {
    @Bean
    public OpenAPI cvServerOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("简历管理系统 API")
                        .description("简历上传、存储和内容提取服务")
                        .version("1.0")
                        .contact(new Contact()
                                .name("Unionman")
                                .email("<EMAIL>")
                                .url("https://www.unionman.com")));
    }
}