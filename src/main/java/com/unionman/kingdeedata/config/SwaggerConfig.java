package com.unionman.kingdeedata.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {
    @Bean
    public OpenAPI cvServerOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("金蝶审计操作程序")
                        .description("金蝶数据导出、修正程序")
                        .version("1.0")
                        .contact(new Contact()
                                .name("Unionman")
                                .email("<EMAIL>")
                                .url("https://www.unionman.com")));
    }
}